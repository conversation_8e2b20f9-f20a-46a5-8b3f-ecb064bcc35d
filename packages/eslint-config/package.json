{"name": "@repo/eslint-config", "type": "module", "version": "0.0.0", "private": true, "exports": {"./*": "./*.js"}, "scripts": {"lint": "eslint .", "lint:fix": "eslint --fix"}, "devDependencies": {"@antfu/eslint-config": "^5.1.0", "@eslint-react/eslint-plugin": "^1.52.3", "@next/eslint-plugin-next": "^15.4.4", "@tanstack/eslint-plugin-query": "^5.78.0", "eslint": "^9.32.0", "eslint-plugin-format": "^1.0.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-turbo": "^2.5.5", "typescript": "^5.8.3", "typescript-eslint": "^8.39.1"}}