{"name": "@repo/api", "type": "module", "version": "0.0.0", "private": true, "exports": {".": "./src/index.ts", "./trpc": "./src/trpc.ts"}, "main": "./src/index.ts", "scripts": {"lint": "eslint .", "lint:fix": "eslint --fix"}, "dependencies": {"@repo/auth": "workspace:*", "@repo/db": "workspace:*", "@repo/definitions": "workspace:*", "@trpc/server": "^11.4.4", "superjson": "^2.2.2", "zod": "^4.0.17"}, "devDependencies": {"@repo/eslint-config": "workspace:*"}}