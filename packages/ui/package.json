{"name": "@repo/ui", "type": "module", "version": "0.0.0", "private": true, "exports": {"./components/*": "./src/components/*.tsx", "./theme.css": "./src/styles/theme.css", "./lib/*": "./src/lib/*.ts", "./hooks/*": "./src/hooks/*.ts"}, "scripts": {"lint": "eslint", "lint:fix": "eslint --fix"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-scroll-area": "^1.2.5", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tooltip": "^1.2.6", "@tabler/icons-react": "^3.34.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "react": "^19.1.1", "react-dom": "^19.1.1", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.6"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@tailwindcss/postcss": "^4.1.5", "@turbo/gen": "^2.5.5", "@types/node": "^22.16.4", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "tailwindcss": "^4.1.5", "typescript": "^5.8.3"}}