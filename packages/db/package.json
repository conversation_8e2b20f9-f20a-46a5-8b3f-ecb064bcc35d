{"name": "@repo/db", "type": "module", "version": "0.0.0", "private": true, "main": "./src/index.ts", "scripts": {"db:generate": "dotenv -e .env -- drizzle-kit generate", "db:migrate": "dotenv -e .env -- drizzle-kit migrate", "db:studio": "dotenv -e .env -- drizzle-kit studio", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"@repo/definitions": "workspace:*", "@t3-oss/env-core": "^0.13.8", "drizzle-orm": "^0.44.2", "postgres": "^3.4.7", "zod": "^4.0.17"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/node": "^22.16.4", "dotenv-cli": "^8.0.0", "drizzle-kit": "^0.31.4"}}