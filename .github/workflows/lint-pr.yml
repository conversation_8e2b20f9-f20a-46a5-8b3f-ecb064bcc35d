name: Lint PR

on:
  pull_request_target:
    types:
      - opened
      - edited
      - synchronize

jobs:
  main:
    name: Validate and Label PR
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
      contents: read
    steps:
      - name: Validate PR title
        uses: amannn/action-semantic-pull-request@v5
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          types: |
            feat
            fix
            docs
            style
            refactor
            perf
            test
            build
            ci
            chore
            revert
            i18n
            ai

      - name: Auto Label PR
        uses: actions/github-script@v7
        with:
          script: |
            const title = context.payload.pull_request.title;
            const prNumber = context.payload.pull_request.number;

            // Mapping from commit type to label
            const typeToLabel = {
              'feat': 'feat',
              'fix': 'fix',
              'docs': 'docs',
              'style': 'style',
              'refactor': 'refactor',
              'perf': 'perf',
              'test': 'test',
              'build': 'build',
              'ci': 'ci',
              'chore': 'chore',
              'revert': 'revert',
              'i18n': 'i18n',
              'ai': 'ai'
            };

            // Extract type from PR title (format: "type: description" or "type(scope): description")
            const match = title.match(/^(\w+)(?:\([^)]*\))?(!)?:\s/);

            if (match) {
              const commitType = match[1];
              const hasBreakingMark = match[2] === '!';
              const label = typeToLabel[commitType];

              // Check for breaking change indicator
              const isBreaking = hasBreakingMark || title.toLowerCase().includes('breaking change');

              if (label) {
                console.log(`Adding label "${label}" to PR #${prNumber} based on type "${commitType}"`);

                const labelsToAdd = [label];

                // Add breaking-change label if it's a breaking change
                if (isBreaking) {
                  labelsToAdd.push('breaking-change');
                  console.log(`Also adding "breaking-change" label due to breaking change indicator`);
                }

                try {
                  await github.rest.issues.addLabels({
                    owner: context.repo.owner,
                    repo: context.repo.repo,
                    issue_number: prNumber,
                    labels: labelsToAdd
                  });

                  console.log(`Successfully added labels: ${labelsToAdd.join(', ')}`);
                } catch (error) {
                  console.error(`Failed to add labels: ${error.message}`);
                }
              } else {
                console.log(`No label mapping found for commit type "${commitType}"`);
              }
            } else {
              console.log(`PR title "${title}" doesn't match conventional commit format`);
            }
