name: Discord Release Notification

on:
  workflow_dispatch:
    inputs:
      tag:
        description: 'Release tag to notify about (e.g., @read-frog/extension@0.10.4, or leave empty for latest)'
        required: false
        type: string
      force_notify:
        description: Force notification even if no release notes found
        required: false
        type: boolean
        default: false

jobs:
  notify:
    name: Send Discord Notification
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v3
        with:
          fetch-depth: 0 # Fetch all history for tags

      - name: Get target tag
        id: tag
        run: |
          if [ -n "${{ github.event.inputs.tag }}" ]; then
            TAG="${{ github.event.inputs.tag }}"
            echo "Using input tag: $TAG"
          else
            # Get latest release tag from GitHub API
            TAG=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
              "https://api.github.com/repos/${{ github.repository }}/releases/latest" \
              | jq -r '.tag_name // ""')

            if [ -z "$TAG" ] || [ "$TAG" = "null" ]; then
              echo "No releases found via API, trying git describe..."
              TAG=$(git describe --abbrev=0 --tags 2>/dev/null || echo "")
            fi

            echo "Found latest tag: $TAG"
          fi

          if [ -z "$TAG" ]; then
            echo "Error: No tag found!"
            echo "Available tags:"
            git tag --list --sort=-version:refname | head -10
            exit 1
          fi

          echo "TAG=$TAG" >> $GITHUB_OUTPUT
          echo "Final tag to use: $TAG"

      - name: Get Release Info
        id: release_info
        run: |
          echo "Getting release info for tag: ${{ steps.tag.outputs.TAG }}"

          # Get release info from GitHub API
          RELEASE_DATA=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
            "https://api.github.com/repos/${{ github.repository }}/releases/tags/${{ steps.tag.outputs.TAG }}")

          # Check if API call was successful
          if echo "$RELEASE_DATA" | jq -e '.message' > /dev/null 2>&1; then
            echo "API Error: $(echo "$RELEASE_DATA" | jq -r '.message')"

            if [ "${{ github.event.inputs.force_notify }}" = "true" ]; then
              echo "Force notify enabled, using minimal release info..."
              RELEASE_NOTES="Manual notification for tag ${{ steps.tag.outputs.TAG }}"
              RELEASE_URL="https://github.com/${{ github.repository }}/releases/tag/${{ steps.tag.outputs.TAG }}"
            else
              echo "Trying to use latest release instead..."
              RELEASE_DATA=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
                "https://api.github.com/repos/${{ github.repository }}/releases/latest")

              if echo "$RELEASE_DATA" | jq -e '.message' > /dev/null 2>&1; then
                echo "Failed to get any release data"
                exit 1
              fi
            fi
          fi

          # Extract release notes and URL if we have valid release data
          if [ "${{ github.event.inputs.force_notify }}" != "true" ]; then
            RELEASE_NOTES=$(echo "$RELEASE_DATA" | jq -r '.body // "No release notes available"')
            RELEASE_URL=$(echo "$RELEASE_DATA" | jq -r '.html_url // ""')
          fi

          # Clean up release notes
          if [ -n "$RELEASE_NOTES" ]; then
            # Remove HTML comments (<!-- ... -->)
            RELEASE_NOTES=$(echo "$RELEASE_NOTES" | sed 's/<!--[^>]*-->//g')

            # Format markdown headers for Discord
            RELEASE_NOTES=$(echo "$RELEASE_NOTES" | \
              sed 's/^## \(.*\)$/**\1**/g' | \
              sed 's/^### \(.*\)$/▸ **\1**/g' | \
              sed 's/^#### \(.*\)$/  • **\1**/g' | \
              sed 's/^##### \(.*\)$/    ◦ **\1**/g')

            # Remove leading/trailing whitespace
            RELEASE_NOTES=$(echo "$RELEASE_NOTES" | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')

            # Limit to Discord embed limit (leaving room for other content)
            if [ ${#RELEASE_NOTES} -gt 900 ]; then
              RELEASE_NOTES=$(echo "$RELEASE_NOTES" | head -c 900)
              RELEASE_NOTES="${RELEASE_NOTES}..."
            fi
          fi

          # Save to file to avoid shell escaping issues
          echo "$RELEASE_NOTES" > /tmp/release_notes.txt

          # Set outputs
          if [ -z "$RELEASE_URL" ]; then
            RELEASE_URL="https://github.com/${{ github.repository }}/releases/tag/${{ steps.tag.outputs.TAG }}"
          fi
          echo "RELEASE_URL=$RELEASE_URL" >> $GITHUB_OUTPUT

          echo "Release URL: $RELEASE_URL"
          echo "Release notes length: ${#RELEASE_NOTES}"

      - name: Send Discord Notification
        run: |
          echo "Sending Discord notification for tag: ${{ steps.tag.outputs.TAG }}"

          # Read release notes from file
          RELEASE_NOTES_RAW=$(cat /tmp/release_notes.txt)

          # Determine if this is a package-specific release or general release
          TAG="${{ steps.tag.outputs.TAG }}"
          if [[ "$TAG" == *"@"* ]]; then
            # Package-specific release (e.g., @read-frog/extension@0.10.4)
            PACKAGE_NAME=$(echo "$TAG" | sed 's/@[^@]*$//')
            PACKAGE_VERSION=$(echo "$TAG" | sed 's/.*@//')

            # Get package emoji
            case "$PACKAGE_NAME" in
              *"extension"*) EMOJI="🐸" ;;
              *"website"*) EMOJI="🌐" ;;
              *"cli"*) EMOJI="⚡" ;;
              *"api"*) EMOJI="🔌" ;;
              *"core"*) EMOJI="⚙️" ;;
              *"sdk"*) EMOJI="🛠️" ;;
              *) EMOJI="📦" ;;
            esac

            PACKAGE_DISPLAY_NAME=$(echo "$PACKAGE_NAME" | sed 's/.*\///g' | sed 's/\b\w/\U&/g')
            TITLE="$EMOJI $PACKAGE_DISPLAY_NAME Release!"
            DESCRIPTION="**$PACKAGE_DISPLAY_NAME** version \`$PACKAGE_VERSION\` has been released!"

            # Build fields using jq for package release
            if [ -n "$RELEASE_NOTES_RAW" ] && [ "$RELEASE_NOTES_RAW" != "No release notes available" ]; then
              FIELDS_JSON=$(jq -n \
                --arg tag "$TAG" \
                --arg release_notes "$RELEASE_NOTES_RAW" \
                '[
                  {
                    "name": "🏷️ Tag",
                    "value": ("`" + $tag + "`"),
                    "inline": true
                  },
                  {
                    "name": "📝 Release Notes",
                    "value": $release_notes,
                    "inline": false
                  }
                ]')
            else
              FIELDS_JSON=$(jq -n \
                --arg tag "$TAG" \
                '[
                  {
                    "name": "🏷️ Tag",
                    "value": ("`" + $tag + "`"),
                    "inline": true
                  }
                ]')
            fi
          else
            # General release
            TITLE="🎉 New Release Published!"
            DESCRIPTION="**${{ github.repository }}** has published a new release!"

            # Build fields using jq for general release
            if [ -n "$RELEASE_NOTES_RAW" ] && [ "$RELEASE_NOTES_RAW" != "No release notes available" ]; then
              FIELDS_JSON=$(jq -n \
                --arg tag "$TAG" \
                --arg release_notes "$RELEASE_NOTES_RAW" \
                '[
                  {
                    "name": "🏷️ Version",
                    "value": ("`" + $tag + "`"),
                    "inline": true
                  },
                  {
                    "name": "📝 Release Notes",
                    "value": $release_notes,
                    "inline": false
                  }
                ]')
            else
              FIELDS_JSON=$(jq -n \
                --arg tag "$TAG" \
                '[
                  {
                    "name": "🏷️ Version",
                    "value": ("`" + $tag + "`"),
                    "inline": true
                  }
                ]')
            fi
          fi

          # Create the JSON payload using jq to ensure proper formatting
          TIMESTAMP=$(date -u +%Y-%m-%dT%H:%M:%S.000Z)
          jq -n \
            --arg title "$TITLE" \
            --arg description "$DESCRIPTION" \
            --arg url "${{ steps.release_info.outputs.RELEASE_URL }}" \
            --argjson fields "$FIELDS_JSON" \
            --arg timestamp "$TIMESTAMP" \
            '{
              embeds: [{
                title: $title,
                description: $description,
                url: $url,
                color: 48253,
                fields: $fields,
                footer: {
                  text: "Manual notification via GitHub Actions",
                  icon_url: "https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png"
                },
                timestamp: $timestamp
              }]
            }' > /tmp/discord_payload.json

          echo "Discord payload:"
          cat /tmp/discord_payload.json

          # Validate JSON before sending
          if ! jq empty /tmp/discord_payload.json 2>/dev/null; then
            echo "Error: Generated invalid JSON payload"
            exit 1
          fi

          # Send to Discord
          RESPONSE=$(curl -s -w "%{http_code}" -H "Content-Type: application/json" \
               -X POST \
               -d @/tmp/discord_payload.json \
               ${{ secrets.DISCORD_WEBHOOK_URL }})

          HTTP_CODE=$(echo "$RESPONSE" | tail -c 4)
          if [ "$HTTP_CODE" -ge 200 ] && [ "$HTTP_CODE" -lt 300 ]; then
            echo "✅ Successfully sent Discord notification (HTTP $HTTP_CODE)"
          else
            echo "❌ Failed to send Discord notification (HTTP $HTTP_CODE)"
            echo "Response: $RESPONSE"
            exit 1
          fi
