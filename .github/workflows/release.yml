name: Release

on:
  push:
    branches:
      - main

concurrency: ${{ github.workflow }}-${{ github.ref }}

jobs:
  release:
    name: Release
    runs-on: ubuntu-latest
    env:
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ vars.TURBO_TEAM }}
      SKIP_ENV_VALIDATION: true
      SKIP_PRE_PUSH: true
    steps:
      - name: Checkout Repo
        uses: actions/checkout@v3

      - name: Setup pnpm
        uses: pnpm/action-setup@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          cache: pnpm

      - name: Install Dependencies
        run: pnpm install
        shell: bash

      - name: Create Release Pull Request or Tag
        id: changesets
        uses: changesets/action@v1
        with:
          commit: 'chore(release): version packages'
          title: 'chore(release): version packages'
          publish: pnpm run release
          createGithubReleases: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Send Discord Notification
        if: steps.changesets.outputs.published == 'true'
        run: |
          echo "Sending Discord notification for published packages..."

          # Get published packages from changesets output
          PUBLISHED_PACKAGES='${{ steps.changesets.outputs.publishedPackages }}'

          if [ -z "$PUBLISHED_PACKAGES" ] || [ "$PUBLISHED_PACKAGES" = "[]" ]; then
            echo "No packages were published"
            exit 0
          fi

          echo "Published packages: $PUBLISHED_PACKAGES"

          # Process each published package individually
          echo "$PUBLISHED_PACKAGES" | jq -c '.[]' | while read -r package_info; do
            if [ -n "$package_info" ]; then
              PACKAGE_NAME=$(echo "$package_info" | jq -r '.name')
              PACKAGE_VERSION=$(echo "$package_info" | jq -r '.version')

              echo "Processing package: $PACKAGE_NAME@$PACKAGE_VERSION"

              # Generate tag name based on changesets convention
              PACKAGE_TAG="$PACKAGE_NAME@$PACKAGE_VERSION"
              PACKAGE_URL="https://github.com/${{ github.repository }}/releases/tag/$PACKAGE_TAG"

              # Get release info and notes for this specific package tag
              RELEASE_NOTES_RAW=""
              SPECIFIC_RELEASE_URL=""

              # Try to get release data for this specific tag
              RELEASE_DATA=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
                "https://api.github.com/repos/${{ github.repository }}/releases/tags/$PACKAGE_TAG" 2>/dev/null || echo '{}')

              # If specific release doesn't exist, try to get the latest release
              if [ "$(echo "$RELEASE_DATA" | jq -r '.id // "null"')" = "null" ]; then
                echo "No specific release found for $PACKAGE_TAG, trying latest release..."
                LATEST_TAG=$(git describe --abbrev=0 --tags 2>/dev/null || echo "")
                if [ -z "$LATEST_TAG" ]; then
                  # Fallback to getting latest release from GitHub API
                  LATEST_TAG=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
                    "https://api.github.com/repos/${{ github.repository }}/releases/latest" \
                    | jq -r '.tag_name // ""')
                fi

                if [ -n "$LATEST_TAG" ] && [ "$LATEST_TAG" != "null" ]; then
                  RELEASE_DATA=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
                    "https://api.github.com/repos/${{ github.repository }}/releases/tags/$LATEST_TAG")
                  SPECIFIC_RELEASE_URL=$(echo "$RELEASE_DATA" | jq -r '.html_url // ""')
                fi
              else
                SPECIFIC_RELEASE_URL=$(echo "$RELEASE_DATA" | jq -r '.html_url // ""')
              fi

              # Use package URL if no specific release URL found
              if [ -z "$SPECIFIC_RELEASE_URL" ]; then
                SPECIFIC_RELEASE_URL="$PACKAGE_URL"
              fi

              # Extract release notes and clean them up
              RELEASE_NOTES_RAW=$(echo "$RELEASE_DATA" | jq -r '.body // "No release notes available"')

              # Remove HTML comments and format headers
              if [ -n "$RELEASE_NOTES_RAW" ] && [ "$RELEASE_NOTES_RAW" != "No release notes available" ]; then
                RELEASE_NOTES_RAW=$(echo "$RELEASE_NOTES_RAW" | sed 's/<!--[^>]*-->//g')
                RELEASE_NOTES_RAW=$(echo "$RELEASE_NOTES_RAW" | \
                  sed 's/^## \(.*\)$/**\1**/g' | \
                  sed 's/^### \(.*\)$/▸ **\1**/g' | \
                  sed 's/^#### \(.*\)$/  • **\1**/g' | \
                  sed 's/^##### \(.*\)$/    ◦ **\1**/g')

                # Remove leading/trailing whitespace
                RELEASE_NOTES_RAW=$(echo "$RELEASE_NOTES_RAW" | sed 's/^[[:space:]]*//' | sed 's/[[:space:]]*$//')

                # Limit to Discord embed limit (leaving room for other content)
                if [ ${#RELEASE_NOTES_RAW} -gt 800 ]; then
                  RELEASE_NOTES_RAW=$(echo "$RELEASE_NOTES_RAW" | head -c 800)
                  RELEASE_NOTES_RAW="${RELEASE_NOTES_RAW}..."
                fi
              fi

              # Get package emoji
              case "$PACKAGE_NAME" in
                *"extension"*) EMOJI="🐸" ;;
                *"website"*) EMOJI="🌐" ;;
                *"cli"*) EMOJI="⚡" ;;
                *"api"*) EMOJI="🔌" ;;
                *"core"*) EMOJI="⚙️" ;;
                *"sdk"*) EMOJI="🛠️" ;;
                *) EMOJI="📦" ;;
              esac

              # Extract display name (e.g., @read-frog/extension -> Extension)
              PACKAGE_DISPLAY_NAME=$(echo "$PACKAGE_NAME" | sed 's/.*\///g' | sed 's/\b\w/\U&/g')

              TITLE="$EMOJI $PACKAGE_DISPLAY_NAME Release!"
              DESCRIPTION="**$PACKAGE_DISPLAY_NAME** version \`$PACKAGE_VERSION\` has been released!"

              # Build fields using jq
              if [ -n "$RELEASE_NOTES_RAW" ] && [ "$RELEASE_NOTES_RAW" != "No release notes available" ]; then
                FIELDS_JSON=$(jq -n \
                  --arg tag "$PACKAGE_TAG" \
                  --arg release_notes "$RELEASE_NOTES_RAW" \
                  '[
                    {
                      "name": "🏷️ Tag",
                      "value": ("`" + $tag + "`"),
                      "inline": true
                    },
                    {
                      "name": "📝 Release Notes",
                      "value": $release_notes,
                      "inline": false
                    }
                  ]')
              else
                FIELDS_JSON=$(jq -n \
                  --arg tag "$PACKAGE_TAG" \
                  '[
                    {
                      "name": "🏷️ Tag",
                      "value": ("`" + $tag + "`"),
                      "inline": true
                    }
                  ]')
              fi

              # Create individual embed for this package
              TIMESTAMP=$(date -u +%Y-%m-%dT%H:%M:%S.000Z)
              EMBED_JSON=$(jq -n \
                --arg title "$TITLE" \
                --arg description "$DESCRIPTION" \
                --arg url "$SPECIFIC_RELEASE_URL" \
                --argjson fields "$FIELDS_JSON" \
                --arg timestamp "$TIMESTAMP" \
                '{
                  title: $title,
                  description: $description,
                  url: $url,
                  color: 48253,
                  fields: $fields,
                  footer: {
                    text: "GitHub Actions",
                    icon_url: "https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png"
                  },
                  timestamp: $timestamp
                }')

              # Create Discord payload for this individual package
              DISCORD_PAYLOAD=$(echo "$EMBED_JSON" | jq '{embeds: [.]}')

              echo "Discord payload for $PACKAGE_NAME:"
              echo "$DISCORD_PAYLOAD"

              # Validate JSON before sending
              if ! echo "$DISCORD_PAYLOAD" | jq empty 2>/dev/null; then
                echo "Error: Generated invalid JSON payload for $PACKAGE_NAME"
                continue
              fi

              # Send individual notification to Discord
              echo "Sending Discord notification for $PACKAGE_NAME@$PACKAGE_VERSION..."
              RESPONSE=$(curl -s -w "%{http_code}" -H "Content-Type: application/json" \
                   -X POST \
                   -d "$DISCORD_PAYLOAD" \
                   ${{ secrets.DISCORD_WEBHOOK_URL }})

              HTTP_CODE=$(echo "$RESPONSE" | tail -c 4)
              if [ "$HTTP_CODE" -ge 200 ] && [ "$HTTP_CODE" -lt 300 ]; then
                echo "✅ Successfully sent Discord notification for $PACKAGE_NAME (HTTP $HTTP_CODE)"
              else
                echo "❌ Failed to send Discord notification for $PACKAGE_NAME (HTTP $HTTP_CODE)"
                echo "Response: $RESPONSE"
              fi

              # Add a small delay between notifications to avoid rate limiting
              sleep 1
            fi
          done
