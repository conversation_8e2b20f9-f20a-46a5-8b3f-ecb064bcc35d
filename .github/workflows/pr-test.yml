name: PR Test

on:
  pull_request:
    branches:
      - main

jobs:
  test:
    runs-on: ubuntu-latest
    env:
      TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
      TURBO_TEAM: ${{ vars.TURBO_TEAM }}

    steps:
      - name: Checkout Rep<PERSON>
        uses: actions/checkout@v4

      - name: Setup pnpm
        uses: pnpm/action-setup@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          cache: pnpm

      - name: Install Dependencies
        run: pnpm install --frozen-lockfile
        shell: bash

      - name: Check for eruda imports
        run: |
          if grep -r "import.*eruda" src/ --include="*.ts" --include="*.tsx" --include="*.js" --include="*.jsx" | grep -v ":.*[[:space:]]*//.*import.*eruda"; then
            echo "❌ Found uncommented eruda imports in the codebase!"
            echo "Please comment out or remove eruda imports before merging."
            exit 1
          else
            echo "✅ No uncommented eruda imports found."
          fi
        shell: bash

      - name: Syncpack lint
        run: pnpm syncpack:lint

      - name: Type check
        run: pnpm type-check

      - name: Build
        run: pnpm build
        env:
          SKIP_ENV_VALIDATION: true
          PUBLIC_REPO_GITHUB_TOKEN: ${{ secrets.PUBLIC_REPO_GITHUB_TOKEN }}

      - name: Run tests
        run: pnpm test
