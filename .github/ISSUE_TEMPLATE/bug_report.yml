name: Report a bug
description: Create a report to help us improve Read Frog
labels: [bug]
title: '[BUG] '
assignees: []
body:
  - type: checkboxes
    id: preflight
    attributes:
      label: Before you report
      description: |
        Please confirm the following before submitting. For questions and quick help, join our Discord community.
        Discord: https://discord.gg/ej45e3PezJ
      options:
        - label: I have searched existing issues to avoid duplicates
          required: true
        - label: I have tried with the latest available version (or can reproduce on latest)
          required: false
  - type: markdown
    attributes:
      value: |
        This template is used for reporting an issue with Read Frog.

        Feature requests should be opened via the [Feature Request Form](https://github.com/mengxi-ream/read-frog/issues/new?assignees=&labels=enhancement&projects=&template=feature_request.yml&title=)
  - type: textarea
    id: expected
    attributes:
      label: Current vs. Expected behavior
      description: |
        A clear and concise description of what happened (e.g., screenshots, logs, errors) and what you expected to happen.

        Skipping this / failure to provide complete information may result in the issue being closed.
      placeholder: 'Following the steps from the previous section, I expected A to happen, but I observed B instead.'
    validations:
      required: true
  - type: textarea
    id: reproduce
    attributes:
      label: To Reproduce
      description: |
        A step-by-step description of how to reproduce the issue. Screenshots can be provided in the issue body as well.
      placeholder: |
        Ex:
        1. Open a news article page
        2. Click the Read Frog icon to open the popup
        3. Enable Immersive Translation
        4. Observe translation not appearing on the page
    validations:
      required: true
  - type: dropdown
    id: areas
    attributes:
      label: Which area(s) are affected? (Select all that apply)
      multiple: true
      options:
        - Browser Extension
        - Website
        - Other
    validations:
      required: true
  - type: input
    id: extension_version
    attributes:
      label: Extension version (if applicable)
      description: Provide the current version of the browser extension
      placeholder: 1.x.x
  - type: textarea
    id: environment
    attributes:
      label: Provide environment information
      description: Please collect the following information and paste the results.
      render: bash
      placeholder: |
        - OS: [e.g. macOS 14.5]
        - Browser: [e.g. Chrome 126, Edge 125]
        - Extension channel/version: [e.g. Chrome Web Store 1.2.3]
        - Website: [e.g. https://readfrog.app, date/time observed]
    validations:
      required: true
  - type: textarea
    id: additional
    attributes:
      label: Additional context
      description: |
        Any extra information that might help us investigate. For example, is it only reproducible online, or locally too? Is the issue only happening in a specific website or browser?
