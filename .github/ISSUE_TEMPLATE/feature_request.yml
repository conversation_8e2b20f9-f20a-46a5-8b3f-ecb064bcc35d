name: Feature request
description: Suggest an idea for Read Frog
title: '[FEATURE] '
labels: [enhancement]
assignees: []
body:
  - type: checkboxes
    id: preflight
    attributes:
      label: Before you begin
      description: |
        Please confirm the following before submitting. For questions and quick help, join our Discord community.
        Discord: https://discord.gg/ej45e3PezJ
      options:
        - label: I have searched existing issues to avoid duplicates
          required: true
        - label: I am willing to contribute a PR for this feature
          required: false
  - type: markdown
    attributes:
      value: |
        This template is used for suggesting a feature for Read Frog.

        Bug reports should be opened via the [Bug Report Form](https://github.com/mengxi-ream/read-frog/issues/new?assignees=&labels=bug&projects=&template=bug_report.yml&title=)
  - type: textarea
    id: problem
    attributes:
      label: Is your feature request related to a problem? Please describe.
      description: A clear and concise description of what the problem is. Ex. I'm always frustrated when [...]
    validations:
      required: true
  - type: textarea
    id: solution
    attributes:
      label: Describe the solution you'd like
      description: A clear and concise description of what you want to happen.
    validations:
      required: true
  - type: dropdown
    id: areas
    attributes:
      label: Which area(s) would this feature affect? (Select all that apply)
      multiple: true
      options:
        - Browser Extension
        - Website
        - Other
    validations:
      required: true
  - type: textarea
    id: context
    attributes:
      label: Additional context
      description: Add any other context or screenshots about the feature request here.
