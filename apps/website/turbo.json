{"extends": ["//"], "tasks": {"build": {"env": ["NODE_ENV", "BETTER_AUTH_SECRET", "BETTER_AUTH_URL", "GOOGLE_CLIENT_ID", "GOOGLE_CLIENT_SECRET", "DATABASE_URL", "PUBLIC_REPO_GITHUB_TOKEN", "VERCEL_URL", "PORT", "SKIP_ENV_VALIDATION"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**", ".source/**"]}, "start": {"cache": false, "persistent": true, "dependsOn": ["build"]}}}