{"extends": "@repo/typescript-config/next.json", "compilerOptions": {"incremental": true, "target": "ES2022", "jsx": "preserve", "lib": ["dom", "dom.iterable", "esnext"], "baseUrl": ".", "module": "esnext", "moduleResolution": "bundler", "paths": {"@/.source": ["./.source/index.ts"], "@/*": ["./src/*"]}, "resolveJsonModule": true, "allowJs": true, "strict": true, "noEmit": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "skipLibCheck": true, "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "src/env.ts"], "exclude": ["node_modules"]}