import type { Locale } from 'next-intl'
import { z } from 'zod'

export const langCodeISO6393Schema = z.enum([
  'eng',
  'cmn',
  'cmn-Hant',
  'yue',
  'spa',
  'rus',
  'arb',
  'ben',
  'hin',
  'por',
  'ind',
  'jpn',
  'fra',
  'deu',
  'jav',
  'kor',
  'tel',
  'vie',
  'mar',
  'ita',
  'tam',
  'tur',
  'urd',
  'guj',
  'pol',
  'ukr',
  'kan',
  'mai',
  'mal',
  'pes',
  'mya',
  'swh',
  'sun',
  'ron',
  'pan',
  'bho',
  'amh',
  'hau',
  'fuv',
  'bos',
  'hrv',
  'nld',
  'srp',
  'tha',
  'ckb',
  'yor',
  'uzn',
  'zlm',
  'ibo',
  'npi',
  'ceb',
  'skr',
  'tgl',
  'hun',
  'azj',
  'sin',
  'koi',
  'ell',
  'ces',
  'mag',
  'run',
  'bel',
  'plt',
  'qug',
  'mad',
  'nya',
  'zyb',
  'pbu',
  'kin',
  'zul',
  'bul',
  'swe',
  'lin',
  'som',
  'hms',
  'hnj',
  'ilo',
  'kaz',
])

export const langCodeISO6391Schema = z.enum([
  'en', // English
  'zh', // Chinese（包含简/繁/粤等；只是在 BCP-47 里再加 -Hans / -Hant / -yue）
  'es',
  'ru',
  'ar',
  'bn',
  'hi',
  'pt',
  'id',
  'ja',
  'fr',
  'de',
  'jv',
  'ko',
  'te',
  'vi',
  'mr',
  'it',
  'ta',
  'tr',
  'ur',
  'gu',
  'pl',
  'uk',
  'kn',
  'ml',
  'fa',
  'my',
  'sw',
  'su',
  'ro',
  'pa',
  'am',
  'ha',
  'ff',
  'bs',
  'hr',
  'nl',
  'sr',
  'th',
  'ku',
  'yo',
  'uz',
  'ms',
  'ig',
  'ne',
  'tl',
  'hu',
  'az',
  'si',
  'el',
  'cs',
  'ny',
  'rw',
  'zu',
  'bg',
  'sv',
  'ln',
  'so',
  'kk',
  'be',
] as const)

export type LangCodeISO6391 = z.infer<typeof langCodeISO6391Schema>

export type LangCodeISO6393 = z.infer<typeof langCodeISO6393Schema>

export const LOCALE_TO_ISO6393: Record<Locale, LangCodeISO6393> = {
  en: 'eng',
  zh: 'cmn',
}

export const LANG_CODE_TO_EN_NAME: Record<LangCodeISO6393, string> = {
  'eng': 'English',
  'cmn': 'Simplified Mandarin Chinese',
  'cmn-Hant': 'Traditional Mandarin Chinese',
  'yue': 'Cantonese', // not supported by franc-min
  'spa': 'Spanish',
  'rus': 'Russian',
  'arb': 'Standard Arabic',
  'ben': 'Bengali',
  'hin': 'Hindi',
  'por': 'Portuguese',
  'ind': 'Indonesian',
  'jpn': 'Japanese',
  'fra': 'French',
  'deu': 'German',
  'jav': 'Javanese (Javanese)',
  'kor': 'Korean',
  'tel': 'Telugu',
  'vie': 'Vietnamese',
  'mar': 'Marathi',
  'ita': 'Italian',
  'tam': 'Tamil',
  'tur': 'Turkish',
  'urd': 'Urdu',
  'guj': 'Gujarati',
  'pol': 'Polish',
  'ukr': 'Ukrainian',
  'kan': 'Kannada',
  'mai': 'Maithili',
  'mal': 'Malayalam',
  'pes': 'Iranian Persian',
  'mya': 'Burmese',
  'swh': 'Swahili (individual language)',
  'sun': 'Sundanese',
  'ron': 'Romanian',
  'pan': 'Panjabi',
  'bho': 'Bhojpuri',
  'amh': 'Amharic',
  'hau': 'Hausa',
  'fuv': 'Nigerian Fulfulde',
  'bos': 'Bosnian (Cyrillic)',
  'hrv': 'Croatian',
  'nld': 'Dutch',
  'srp': 'Serbian (Cyrillic)',
  'tha': 'Thai',
  'ckb': 'Central Kurdish',
  'yor': 'Yoruba',
  'uzn': 'Northern Uzbek (Cyrillic)',
  'zlm': 'Malay (individual language) (Arabic)',
  'ibo': 'Igbo',
  'npi': 'Nepali (individual language)',
  'ceb': 'Cebuano',
  'skr': 'Saraiki',
  'tgl': 'Tagalog',
  'hun': 'Hungarian',
  'azj': 'North Azerbaijani (Cyrillic)',
  'sin': 'Sinhala',
  'koi': 'Komi-Permyak',
  'ell': 'Modern Greek (1453-)',
  'ces': 'Czech',
  'mag': 'Magahi',
  'run': 'Rundi',
  'bel': 'Belarusian',
  'plt': 'Plateau Malagasy',
  'qug': 'Chimborazo Highland Quichua',
  'mad': 'Madurese',
  'nya': 'Nyanja',
  'zyb': 'Yongbei Zhuang',
  'pbu': 'Northern Pashto',
  'kin': 'Kinyarwanda',
  'zul': 'Zulu',
  'bul': 'Bulgarian',
  'swe': 'Swedish',
  'lin': 'Lingala',
  'som': 'Somali',
  'hms': 'Southern Qiandong Miao',
  'hnj': 'Hmong Njua',
  'ilo': 'Iloko',
  'kaz': 'Kazakh',
}

export const LANG_CODE_TO_LOCALE_NAME: Record<LangCodeISO6393, string> = {
  'eng': 'English',
  'cmn': '简体中文',
  'cmn-Hant': '繁體中文',
  'yue': '粵語',
  'spa': 'Español',
  'rus': 'Русский',
  'arb': 'العربية',
  'ben': 'বাংলা',
  'hin': 'हिन्दी',
  'por': 'Português',
  'ind': 'Bahasa Indonesia',
  'jpn': '日本語',
  'fra': 'Français',
  'deu': 'Deutsch',
  'jav': 'Basa Jawa',
  'kor': '한국어',
  'tel': 'తెలుగు',
  'vie': 'Tiếng Việt',
  'mar': 'मराठी',
  'ita': 'Italiano',
  'tam': 'தமிழ்',
  'tur': 'Türkçe',
  'urd': 'اردو',
  'guj': 'ગુજરાતી',
  'pol': 'Polski',
  'ukr': 'Українська',
  'kan': 'ಕನ್ನಡ',
  'mai': 'मैथिली',
  'mal': 'മലയാളം',
  'pes': 'فارسی',
  'mya': 'မြန်မာစာ',
  'swh': 'Kiswahili',
  'sun': 'Basa Sunda',
  'ron': 'Română',
  'pan': 'ਪੰਜਾਬੀ',
  'bho': 'भोजपुरी',
  'amh': 'አማርኛ',
  'hau': 'Hausa',
  'fuv': 'Fulfulde',
  'bos': 'Босански',
  'hrv': 'Hrvatski',
  'nld': 'Nederlands',
  'srp': 'Српски',
  'tha': 'ไทย',
  'ckb': 'کوردیی ناوەندی',
  'yor': 'Yorùbá',
  'uzn': 'Ўзбекча',
  'zlm': 'بهاس ملايو',
  'ibo': 'Asụsụ Igbo',
  'npi': 'नेपाली',
  'ceb': 'Cebuano',
  'skr': 'سرائیکی',
  'tgl': 'Tagalog',
  'hun': 'Magyar',
  'azj': 'Азәрбајҹан дили',
  'sin': 'සිංහල',
  'koi': 'Перем Коми кыв',
  'ell': 'Ελληνικά',
  'ces': 'Čeština',
  'mag': 'मगही',
  'run': 'Ikirundi',
  'bel': 'Беларуская',
  'plt': 'Fiteny Malagasy',
  'qug': 'Kichwa',
  'mad': 'Madhurâ',
  'nya': 'Chinyanja',
  'zyb': 'Yongbei Bouxcuengh',
  'pbu': 'پښتو',
  'kin': 'Kinyarwanda',
  'zul': 'isiZulu',
  'bul': 'Български',
  'swe': 'Svenska',
  'lin': 'Lingála',
  'som': 'Af Soomaali',
  'hms': 'Hmongb Shuad',
  'hnj': 'Hmong Njua',
  'ilo': 'Ilokano',
  'kaz': 'Қазақ тілі',
}

export const ISO6393_TO_6391: Record<
  LangCodeISO6393,
  LangCodeISO6391 | undefined
> = {
  'eng': 'en',
  'cmn': 'zh',
  'cmn-Hant': 'zh',
  'yue': 'zh',
  'spa': 'es',
  'rus': 'ru',
  'arb': 'ar',
  'ben': 'bn',
  'hin': 'hi',
  'por': 'pt',
  'ind': 'id',
  'jpn': 'ja',
  'fra': 'fr',
  'deu': 'de',
  'jav': 'jv',
  'kor': 'ko',
  'tel': 'te',
  'vie': 'vi',
  'mar': 'mr',
  'ita': 'it',
  'tam': 'ta',
  'tur': 'tr',
  'urd': 'ur',
  'guj': 'gu',
  'pol': 'pl',
  'ukr': 'uk',
  'kan': 'kn',
  'mai': undefined, // 无 2-letter -> undefined
  'mal': 'ml',
  'pes': 'fa', // Iranian Persian ⇢ fa
  'mya': 'my',
  'swh': 'sw',
  'sun': 'su',
  'ron': 'ro',
  'pan': 'pa',
  'bho': undefined,
  'amh': 'am',
  'hau': 'ha',
  'fuv': 'ff', // Fulah
  'bos': 'bs',
  'hrv': 'hr',
  'nld': 'nl',
  'srp': 'sr',
  'tha': 'th',
  'ckb': 'ku', // 有人也写 'ckb'，但 ISO-639-1 是 ku
  'yor': 'yo',
  'uzn': 'uz',
  'zlm': 'ms', // Malay
  'ibo': 'ig',
  'npi': 'ne',
  'ceb': undefined,
  'skr': undefined,
  'tgl': 'tl',
  'hun': 'hu',
  'azj': 'az',
  'sin': 'si',
  'koi': undefined,
  'ell': 'el',
  'ces': 'cs',
  'mag': undefined,
  'run': undefined,
  'bel': 'be',
  'plt': undefined,
  'qug': undefined,
  'mad': undefined,
  'nya': 'ny',
  'zyb': undefined,
  'pbu': undefined,
  'kin': 'rw',
  'zul': 'zu',
  'bul': 'bg',
  'swe': 'sv',
  'lin': 'ln',
  'som': 'so',
  'hms': undefined,
  'hnj': undefined,
  'ilo': undefined,
  'kaz': 'kk',
}

export const langLevel = z.enum(['beginner', 'intermediate', 'advanced'])
export type LangLevel = z.infer<typeof langLevel>
