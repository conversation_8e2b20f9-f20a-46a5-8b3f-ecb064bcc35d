{"name": "@read-frog/website", "version": "1.0.2", "private": true, "scripts": {"build": "next build", "dev": "next dev -p 8888", "dev:edge": "next dev -p 8888", "lint": "eslint", "lint:fix": "eslint --fix", "postinstall": "fumadocs-mdx", "start": "next start -p 8888"}, "dependencies": {"@orama/tokenizers": "^3.1.6", "@repo/api": "workspace:*", "@repo/auth": "workspace:*", "@repo/db": "workspace:*", "@repo/definitions": "workspace:*", "@repo/ui": "workspace:*", "@t3-oss/env-nextjs": "^0.13.8", "@tabler/icons-react": "^3.34.0", "@tanstack/react-query": "^5.84.2", "@trpc/client": "^11.4.4", "@trpc/server": "^11.4.4", "@trpc/tanstack-react-query": "^11.4.4", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "better-auth": "^1.2.12", "class-variance-authority": "^0.7.1", "fumadocs-core": "^15.6.3", "fumadocs-mdx": "^11.6.11", "fumadocs-ui": "^15.6.3", "lucide-react": "^0.507.0", "motion": "^12.10.0", "next": "^15.4.4", "next-intl": "^4.1.0", "react": "^19.1.1", "react-dom": "^19.1.1", "server-only": "^0.0.1", "superjson": "^2.2.2", "zod": "^4.0.17"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@tailwindcss/postcss": "^4.1.5", "@tailwindcss/typography": "^0.5.16", "@types/mdx": "^2.0.13", "@types/node": "^22.16.4", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "jiti": "^1.21.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.5", "typescript": "^5.8.3"}}