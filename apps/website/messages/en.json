{"home": {"title": "Learn Languages in Depth", "subtitle": "Translate and understand any webpage instantly with AI assistance", "install": {"on": "Install on", "chrome": "Chrome", "edge": "Edge"}, "experience": "Experience the power of", "aiTranslation": "AI Translation & Understanding", "poweredBy": "Powered by leading AI models"}, "footer": {"description": "Learn languages deeply and effortlessly with AI.", "copyright": "Copyright © {year} - All rights reserved.", "contact": "Contact", "legal": "Legal", "community": "Community", "feedback": "<PERSON><PERSON><PERSON>", "discord": "Discord", "wechat": "WeChat", "businessPartnership": "Business Partnership", "termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy", "github": "<PERSON><PERSON><PERSON>", "creatorIntro": "Hey Curious 👋 I'm {name}, the creator of {appName}. Follow my work on", "or": "or"}, "metadata": {"appName": "<PERSON>", "appDefaultTitle": "<PERSON>", "appTitleTemplate": "%s | Read Frog", "appDescription": "Master languages effortlessly and deeply with AI, right in your browser."}, "guide": {"continue": "Continue", "finish": "Finish", "skip": "<PERSON><PERSON>", "step1": {"title": "👋 You are almost there! Set your native language", "description": "You have successfully installed Read Frog extension. With just a few steps, you are ready to start your language learning journey.", "nativeLanguage": "Native language"}, "step2": {"title": "📌 Pin Read Frog extension icon", "description": "The Read Frog extension icon provides the main functionality and configuration of the extension. Please refer to the example image and pin the extension to continue.", "hint": {"pinned": "You can continue to the next step now.", "unpinned": "Hint: Please pin the extension to continue the next step!"}}, "step3": {"title": "🌐 Try to translate the right side page", "description": "Click the Read Frog extension icon, then click the \"Translate\" button.", "hint": {"translated": "You can continue to the next step now.", "notTranslated": "Hint: You can continue after translation."}}, "step4": {"title": "🎉 You are ready to go!", "description": "You have already mastered the basic functions of the companion frog! If you want to activate more advanced features, please use the tutorial below for configuration.", "tutorial": "Advanced Tutorial", "hint": "Hint: You need to set API Key to activate more advanced features."}}, "language": {"eng": "English", "cmn": "Simplified Mandarin Chinese", "cmn-Hant": "Traditional Mandarin Chinese", "yue": "Cantonese", "spa": "Spanish", "rus": "Russian", "arb": "Standard Arabic", "ben": "Bengali", "hin": "Hindi", "por": "Portuguese", "ind": "Indonesian", "jpn": "Japanese", "fra": "French", "deu": "German", "jav": "Javanese (Javanese)", "kor": "Korean", "tel": "Telugu", "vie": "Vietnamese", "mar": "Marathi", "ita": "Italian", "tam": "Tamil", "tur": "Turkish", "urd": "Urdu", "guj": "Gujarati", "pol": "Polish", "ukr": "Ukrainian", "kan": "Kannada", "mai": "<PERSON><PERSON><PERSON>", "mal": "Malayalam", "pes": "Iranian Persian", "mya": "Burmese", "swh": "Swahili (individual language)", "sun": "Sundanese", "ron": "Romanian", "pan": "Panjabi", "bho": "B<PERSON>jpuri", "amh": "Amharic", "hau": "Hausa", "fuv": "Nigerian Fulfulde", "bos": "Bosnian (Cyrillic)", "hrv": "Croatian", "nld": "Dutch", "srp": "Serbian (Cyrillic)", "tha": "Thai", "ckb": "Central Kurdish", "yor": "Yoruba", "uzn": "Northern Uzbek (Cyrillic)", "zlm": "Malay (individual language) (Arabic)", "ibo": "Igbo", "npi": "Nepali (individual language)", "ceb": "Cebuano", "skr": "Saraiki", "tgl": "Tagalog", "hun": "Hungarian", "azj": "North Azerbaijani (Cyrillic)", "sin": "Sinhala", "koi": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ell": "Modern Greek (1453-)", "ces": "Czech", "mag": "<PERSON><PERSON><PERSON>", "run": "<PERSON><PERSON>", "bel": "Belarusian", "plt": "Plateau Malagasy", "qug": "Chimborazo Highland Quichua", "mad": "<PERSON><PERSON><PERSON>", "nya": "<PERSON><PERSON><PERSON>", "zyb": "Yongbei <PERSON>", "pbu": "Northern Pashto", "kin": "Kinyarwanda", "zul": "Zulu", "bul": "Bulgarian", "swe": "Swedish", "lin": "Lingala", "som": "Somali", "hms": "Southern Qiandong Miao", "hnj": "Hmong Njua", "ilo": "Iloko", "kaz": "Kazakh"}}