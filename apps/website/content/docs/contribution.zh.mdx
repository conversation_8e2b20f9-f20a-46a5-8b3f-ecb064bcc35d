---
title: 贡献代码
description: 一起构建更好的语言学习产品
icon: GitPullRequestArrow
---

## 启动项目

### 步骤 1: Fork 项目并克隆到本地

```bash
# 从你的 fork 克隆项目
git clone https://github.com/xxxxx/read-frog.git

# 进入项目目录
cd read-frog
```

### 步骤 2: 设置并初始化本地数据库
你可以在本地设置任何你选择的 Postgres 数据库。建议使用 Supabase 本地开发。如果使用非 Supabase 部署，请跳过步骤 2.1。

#### 步骤 2.1: Supabase 本地部署
要设置项目所需的数据库，请参考 [Supabase 本地开发设置指南](https://supabase.com/docs/guides/local-development?queryGroups=package-manager&package-manager=brew)
来创建用于本地开发的 Supabase 实例。完成此步骤后，项目根目录下会创建一个新的 `supabase` 文件夹。请不要提交此文件夹，因为它仅用于本地开发。

初始化后，运行以下命令检查本地实例运行状态：
```bash
supabase status
```
你会看到类似以下的输出：
```
         API URL: http://xxxxxxx
     GraphQL URL: http://xxxxxxx
  S3 Storage URL: http://xxxxxxx
          DB URL: postgresql://xxxxxxx
      Studio URL: http://xxxxxxx
    Inbucket URL: http://xxxxxxx
      JWT secret: xxxxxxx
        anon key: xxxxxxx
service_role key: xxxxxxx
   S3 Access Key: xxxxxxx
   S3 Secret Key: xxxxxxx
       S3 Region: xxxxxxx
```
记下 `DB URL` 的值，以便在后续步骤中使用。

#### 步骤 2.2: 初始化本地数据库模式
在 `<项目根目录>/packages/db/` 下创建文件 `.env`。在该文件中，定义变量 `DATABASE_URL` 并填入完整的数据库连接 URL。
如果使用步骤 2.1 中的 Supabase，请填入该步骤中获得的 `DB URL`。

示例 `<项目根目录>/packages/db/.env`：
```
DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres
NODE_ENV=development
```

或者你可以复制示例文件：
```bash
# 在 Unix/Linux/macOS 上：
cp packages/db/.env.example packages/db/.env

# 在 Windows (命令提示符)：
copy packages\db\.env.example packages\db\.env

# 在 Windows (PowerShell)：
Copy-Item packages/db/.env.example packages/db/.env
```

然后，运行以下命令初始化数据库模式：
```bash
pnpm db:migrate
```

### 步骤 3: 设置环境变量
在 `<项目根目录>/apps/website/` 下创建文件 `.env.local`。在该文件中，为 `<项目根目录>/apps/website/env.ts` 中声明的所有环境变量定义值。特别是：
- `BETTER_AUTH_SECRET`: 设置任何值都可以。它被用作加密的种子。
- `BETTER_AUTH_URL`: 与网站 URL 相同。
- `GOOGLE_CLIENT_ID` 和 `GOOGLE_CLIENT_SECRET`: Google SSO 密钥。如果不需要本地登录，设置随机值即可。否则，在 Google 上申请一个。
- `DATABASE_URL`: 连接到 Postgres 数据库的完整 URL。如果使用步骤 2.1 中的 Supabase，请填入该步骤中获得的 `DB URL`。
- `PUBLIC_REPO_GITHUB_TOKEN`: 用于显示 Github 星标数。(可选)

示例 `<项目根目录>/apps/website/.env.local`：
```
BETTER_AUTH_SECRET=<某个_BETTER_AUTH_SECRET>
BETTER_AUTH_URL=http://localhost:8888

GOOGLE_CLIENT_ID=<某个_GOOGLE_CLIENT_ID>
GOOGLE_CLIENT_SECRET=<某个_GOOGLE_CLIENT_SECRET>

DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres

PUBLIC_REPO_GITHUB_TOKEN=<某个_PUBLIC_REPO_GITHUB_TOKEN> (可选)
```

或者你可以复制示例文件：
```bash
# 在 Unix/Linux/macOS 上：
cp apps/website/.env.example apps/website/.env.local

# 在 Windows (命令提示符)：
copy apps\website\.env.example apps\website\.env.local

# 在 Windows (PowerShell)：
Copy-Item apps/website/.env.example apps/website/.env.local
```

### 步骤 4: 启动开发服务器
运行以下命令启动本地开发服务器：
```bash
pnpm dev
```
启动后，在以下地址访问主网站：http://localhost:8888/。

<Callout title="在特定浏览器中打开扩展">
  你可以修改 `apps/extension` 目录下的 `web-ext.config.ts` 文件，显式指定浏览器路径。
  ```javascript
  // apps/extension/web-ext.config.ts
  import { defineWebExtConfig } from 'wxt';

  export default defineWebExtConfig({
    binaries: {
        chrome: "path/to/your/chrome.exe",
        firefox: "path/to/your/firefox.exe",
        edge: "path/to/your/edge.exe"
	  }
  });
  ```
</Callout>

<Callout title="pnpm dev 无法自动加载扩展">
  如果你使用 Chrome 137 或更高版本，你需要下载 [Chrome for
  Testing](https://developer.chrome.com/blog/chrome-for-testing/) 进行开发。
  请参考[详细说明](https://wxt.dev/guide/essentials/config/browser-startup.html)。
</Callout>

<Callout title="持久化 Chrome 配置文件">
  **默认情况下，web-ext 每次运行开发脚本时都会创建新配置文件。**

  **只有 Chromium 浏览器支持使用 `--user-data-dir` 标志创建持久化配置文件：**

  ```typescript
  export default defineWebExtConfig({
    chromiumArgs: ['--user-data-dir=./.wxt/chrome-data'],
  });
  ```

  **好处：** 配置文件在开发会话间保持，所以你可以：
  - 安装开发工具扩展
  - 记住登录状态
  - 保持浏览器设置

  **💡 提示：** 可以为 `--user-data-dir` 使用任何目录。当前配置为每个项目创建配置文件。
</Callout>

## 提交代码

### 创建新的分支

```bash
# 如果是新功能
git checkout -b feat/the-feature

# 如果是修复 bug
git checkout -b fix/the-bug

# 如果是文档调整
git checkout -b docs/the-docs
```

### 分支合并

当远程分支(main)更新时，会导致我们的 Pull Request 存在冲突，你可以通过提前合并远程分支来解决
```bash 
# 切到 main 分支 
git checkout main

# 同步上游仓库
git pull upstream main

# 切回开发分支
git checkout docs/xxxx

# 同步远程分支
git rebase main

# 如果 rebase 后还有冲突，推送一次
git push --force-with-lease origin docs/xxxx

# 如果没有冲突，推送代码
git push origin docs/xxxx
```

<Callout title="git 同步上游仓库">
  如果你在同步上游仓库时遇到该错误
  ```bash
  fatal: 'upstream' does not appear to be a git repository
  fatal: Could not read from remote repository.
  ```
  请尝试手动添加上游仓库
  ```bash
  git remote add upstream https://github.com/mengxi-ream/read-frog
  ```
</Callout>

## 发起 Pull Request

发起 Pull Request 前，需要先添加 changeset
```bash
# 添加 changeset
pnpm changeset

# 选择本次调整的 packages
# Which packages would you like to include?...
[] @read-frog/website
[] @read-frog/extension

# 为 packages 选择需要变更的版本类型
# Which packages should have a major bump? (主版本)
( ) all packages
  ( ) @read-frog/website@0.1.0
  ( ) @read-frog/extension@0.1.0
# Which packages should have a minor bump? (次要版本)
( ) all packages
  ( ) @read-frog/website@0.1.0
  ( ) @read-frog/extension@0.1.0
# The following packages will be patch bumped (补丁版本)
( ) all packages
  ( ) @read-frog/website@0.1.0
  ( ) @read-frog/extension@0.1.0

# 提供 summary
Summary >> ....
```

`pnpm changeset` 会在 `.changeset` 文件中生成 `[uniqId].md`，可在生成完成后再次调整本次变更的描述。

生成完成后再发起 Pull Request，然后等待合并。

## Commit 规范

我们使用 [Conventional Commits](https://www.conventionalcommits.org/) 规范来编写 commit message。

## 设置环境变量

在开发扩展时，为了自动加载环境变量，例如开发环境的 API 密钥，您可以在 `apps/extension` 目录中创建一个 `.env.local` 文件。

```bash
# apps/extension/.env.local
WXT_OPENAI_API_KEY=xxx
WXT_DEEPSEEK_API_KEY=xxx
```

## 在中国大陆跳过谷歌测试

如果你是中国大陆贡献者，那么当你要 push 代码的时候你得用

```bash
# MAC
SKIP_FREE_API=true git push

# WINDOWS
$env:SKIP_FREE_API='true'
git push
```

为什么要这样做呢？因为在你 push 代码的时候会做代码测试，但是谷歌的免费 API 在国内无法访问，这会导致测试失败，从而 push 失败，我们通过设置 `SKIP_FREE_API` 环境变量来跳过谷歌测试。
