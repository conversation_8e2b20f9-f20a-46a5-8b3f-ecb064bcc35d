---
title: 配置 API Key
description: 配置陪读蛙的 API Key，来启动 AI 翻译服务
icon: Key
---

## 为什么要配置 API key

API Key 就像是启动 AI 的 "🔑 钥匙"，它能让我们的软件连接到强大的 AI 翻译服务。

这些 AI 翻译服务由 OpenAI 或者 DeepSeek 这样的商家提供，所以我们需要向他们申请 API Key，然后填写到我们的软件中。

## 配置方法

通过点击弹出窗口或浮动按钮中的 `选项` 按钮进入扩展设置。

![选项](/tutorial/api-key/options.gif)

输入你的 OpenAI 或 DeepSeek API key。

### 如何获取 DeepSeek API Key

你可以查看[官方指南](https://api-docs.deepseek.com/)或者观看下面的视频。

<iframe
  src="//player.bilibili.com/player.html?isOutside=true&aid=114340233483711&bvid=BV1JsopY5E3g&cid=29423831641&p=1"
  scrolling="no"
  border="0"
  framespacing="0"
  allowfullscreen="true"
  className="w-full aspect-video"
/>

### 如何获取 OpenAI API Key

你可以查看[官方指南](https://help.openai.com/en/articles/4936850-where-do-i-find-my-openai-api-key)或观看下面的视频。

<iframe
  src="https://www.youtube-nocookie.com/embed/SzPE_AE0eEo"
  className="w-full aspect-video"
  allowFullScreen
/>
