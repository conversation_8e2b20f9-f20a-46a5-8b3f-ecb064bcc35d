---
title: Ollama
description: 陪读蛙可以借助本地部署的 Ollama 服务器，来实现本地 AI 离线翻译功能
---

## Ollama 服务器是什么？

Ollama 服务器是一款主打轻量化、便捷性的开源 AI 模型运行平台，让用户无需复杂配置，即可在本地轻松部署各类大语言模型（LLMs）与 AI 模型。它就像一个 “AI 模型管家”，将繁琐的模型下载、配置、运行流程简化为几条命令，哪怕是 AI 部署新手，也能快速上手。

## Ollama 本地部署教程

### 1 下载安装程序

你可以从  ollama.ai 官网下载安装程序 [here](https://ollama.ai/).

### 2 运行安装程序

请不要直接点击下载的 `.exe` 文件来安装(因为他会默认安装在C盘). 相反，你最好打开windows的cmd窗口，把安装包拖到cmd窗口中，并输入以下命令：

```bash
OllamaSetup.exe /DIR="D:\Your\Path"
```

这将会安装在D盘的 `Your\Path` 文件夹中。

### 3 配置 ollama 的跨域支持

请配置以下的环境变量：


- API服务监听地址：`OLLAMA_HOST=0.0.0.0`
- 允许跨域访问：`OLLAMA_ORIGINS=*`
- 模型文件下载位置：`OLLAMA_MODELS=F:\ollama\models`
  （此设置与跨域无关，但很有必要，因为默认模型下载位置在C盘，可能会占满你的C盘空间，需手动指定模型存储路径。）

以上的环境变量配置完毕后请重启电脑

## Ollama 常用命令

### 1 检查Ollama版本并且启动ollama服务器

```bash
ollama version
ollama start
```

用于检查当前安装的Ollama版本，以确认安装成功并查看版本详细信息，然后启动ollama服务器

ollama服务器默认地址为：`http://127.0.0.1:11434` 在插件的设置中请求地址已经写好了，API Key 在默认配置启动下可以随意填写，但模型名称必须与 ollama list 列出的名称完全一致。

### 2 搜索可用模型

```bash
ollama search [关键词]
```

示例：搜索与LLaMA相关的模型：

```bash
ollama search llama
```

此命令会列出与关键词匹配的模型，包括模型名称和描述，以帮助用户选择所需模型。

### 3 拉取模型

```bash
ollama pull [模型名称]
```

示例：拉取`llama2`模型：

```bash
ollama pull llama2
```

执行此命令后，Ollama会从模型仓库下载指定模型到本地系统，以供离线使用。

### 4 运行模型

```bash
ollama run [模型名称]
```

示例：运行`llama2`模型并与其交互：

```bash
ollama run llama2
```

运行后，在命令行中输入问题或命令，模型将返回相应的回答。您还可以在运行时添加参数来调整模型行为，例如：

```bash
ollama run llama2 --system "你是一名专业翻译。将用户的输入翻译成英语。"
```

使用`--system`参数设置模型的角色或任务描述。

### 5 停止运行中的模型

```bash
ollama stop [模型名称]
```

示例：停止运行中的`llama2`模型：

```bash
ollama stop llama2
```

此命令会关闭运行中的模型进程并释放系统资源。

### 6 列出已下载的模型

```bash
ollama list
```

此命令会显示所有本地下载的模型，包括名称、大小和创建时间，以帮助用户管理本地模型资源。

### 7 删除模型

```bash
ollama delete [模型名称]
```

示例：删除未使用的`llama2`模型：

```bash
ollama delete llama2
```

执行后，模型将从本地系统中移除，释放磁盘空间。

## Ollama 使用建议

推荐模型：`gemma3:1b`（大小815MB）。该模型麻雀虽小五脏俱全，翻译表现良好且系统负载低（支持CPU运行）。需避免使用如`deepseek`这类推理模型，因其会输出推理过程，导致翻译质量和速度较差。

