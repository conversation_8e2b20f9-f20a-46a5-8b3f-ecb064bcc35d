---
title: DOM Node Interface Hierarchy
description: The following content will provide a detailed introduction to the inheritance relationships between the three interfaces `Node`, `Element`, and `HTMLElement` in the DOM, as well as their respective main subtypes.
---

Through this document, you will clearly understand:

1. `Node` as the foundation of all DOM nodes, including what node types it contains (such as document, element, text, comment, etc.).
2. The role of `Element` on top of `Node`, and how it divides into different branches like HTML, SVG, MathML, etc.
3. `HTMLElement` as the base class for all HTML elements, and what common specific interfaces it has underneath (such as `<div>`, `<input>`, `<canvas>`, etc.).
4. Finally, provide a complete inheritance tree to help you intuitively grasp the full picture from the top-level generic nodes to the bottom-level specific tag interfaces.

## I. Conceptual Analysis of the Three Levels

### 1. `Node` (Interface)

- **Definition**  
  `Node` is the most fundamental interface in the entire DOM (Document Object Model) tree, located at the top of the inheritance chain. Any "node" in the document tree (including elements, text, comments, the document itself, etc.) inherits from `Node`.
- **Main Characteristics**
  - Has universal properties and methods, such as `.parentNode`, `.childNodes`, `.appendChild()`, `.removeChild()`, etc.
  - Can represent **document nodes** (`Document`), **element nodes** (`Element`), **text nodes** (`Text`), **comment nodes** (`Comment`), **document fragments** (`DocumentFragment`), and other types.
  - Hierarchically positioned at:
    ```
    EventTarget → Node
    ```
    In other words, `Node` itself inherits from `EventTarget`, thus having event listening and dispatching capabilities.

### 2. `Element` (Interface, inherits from `Node`)

- **Definition**  
  `Element` is also an interface that inherits from `Node`, representing "element nodes". Compared to `Node`, `Element` additionally provides element-related properties and methods, such as `.tagName`, `.getAttribute()` / `.setAttribute()`, `.classList`, `.querySelector()`, etc.
- **Main Characteristics**
  - As long as it's a tag node in HTML or XML (e.g., `<div>`, `<span>`, `<svg>`, `<circle>`, `<math>`, custom elements, etc.), it's classified as `Element` or its subtypes in the DOM.
  - In the inheritance chain:
    ```
    EventTarget → Node → Element
    ```
  - `Element` itself is further subdivided into:
    ```
    ├── HTMLElement   (Base class for all HTML tags)
    ├── SVGElement    (Base class for all SVG elements)
    ├── MathMLElement (Base class for all MathML elements)
    └── (And possibly other XML/custom element types in the future...)
    ```
  - Each type has its own additional methods and properties. For example, `SVGElement` provides SVG-specific methods like `getBBox()`, `getCTM()`, while `HTMLElement` provides HTML-specific properties and methods like `.innerText`, `.style`, `.dataset`, `.click()`, etc.

### 3. `HTMLElement` (Interface, inherits from `Element`)

- **Definition**  
  `HTMLElement` is the common parent class for all standard **HTML elements** (`<div>`, `<span>`, `<input>`, `<form>`, etc.) corresponding interfaces. In browsers, when you write `<div>...</div>`, what you eventually get in JS is an instance that inherits from `HTMLElement` — specifically of type `HTMLDivElement`.
- **Main Characteristics**
  - Provides properties and methods common to HTML elements, such as:
    - Global attributes: `.id`, `.className`, `.title`, `.dataset`
    - Layout/dimension related: `.clientWidth`, `.offsetHeight`, `.getBoundingClientRect()`
    - Interaction related: `.click()`, `.focus()`, `.blur()`
    - Form related: For form elements, there are also `.disabled`, `.value`, `.checked`, etc.
  - In the inheritance chain:
    ```
    EventTarget → Node → Element → HTMLElement
    ```
  - **All specific HTML element types** (such as `<a>`, `<button>`, `<canvas>`, etc.) inherit from `HTMLElement` and each extends additional properties and methods (e.g., `HTMLCanvasElement` has `.getContext()`, `HTMLAnchorElement` has `.href`, `.target`, etc.).

---

## II. Complete Inheritance Tree (Tree Structure)

Below is a tree diagram showing the inheritance system from `Node` downward, listing common subtypes. For clarity, the entire tree is written in several levels.

```
EventTarget
└─ Node
   ├─ Document
   │  ├─ HTMLDocument  (Usually the specific type of the "document" object in browsers)
   │  ├─ XMLDocument   (In XML environments)
   │  └─ …(Other document types, depending on environment)
   │
   ├─ DocumentType      (Node type corresponding to <!DOCTYPE …>)
   │
   ├─ DocumentFragment  ("Document fragment" for temporarily storing child nodes; e.g., object returned by document.createDocumentFragment())
   │  └─ ShadowRoot     (Node type generated by attachShadow, inherits from DocumentFragment)
   │
   ├─ Element
   │   ├─ HTMLElement
   │   │   ├─ HTMLAnchorElement          (<a>)
   │   │   ├─ HTMLAreaElement            (<area>)
   │   │   ├─ HTMLAudioElement           (<audio>)
   │   │   ├─ HTMLBRElement              (<br>)
   │   │   ├─ HTMLBaseElement            (<base>)
   │   │   ├─ HTMLBodyElement            (<body>)
   │   │   ├─ HTMLButtonElement          (<button>)
   │   │   ├─ HTMLCanvasElement          (<canvas>)
   │   │   ├─ HTMLDListElement           (<dl>)
   │   │   ├─ HTMLDataElement            (<data>)
   │   │   ├─ HTMLDataListElement        (<datalist>)
   │   │   ├─ HTMLDialogElement          (<dialog>)
   │   │   ├─ HTMLDivElement             (<div>)
   │   │   ├─ HTMLEmbedElement           (<embed>)
   │   │   ├─ HTMLFieldSetElement        (<fieldset>)
   │   │   ├─ HTMLFormElement            (<form>)
   │   │   ├─ HTMLFrameSetElement        (<frameset>, deprecated)
   │   │   ├─ HTMLHRElement              (<hr>)
   │   │   ├─ HTMLHeadElement            (<head>)
   │   │   ├─ HTMLHeadingElement         (<h1> ~ <h6>)
   │   │   ├─ HTMLHtmlElement            (<html>)
   │   │   ├─ HTMLIFrameElement          (<iframe>)
   │   │   ├─ HTMLImageElement           (<img>)
   │   │   ├─ HTMLInputElement           (<input>)
   │   │   ├─ HTMLLIElement              (<li>)
   │   │   ├─ HTMLLabelElement           (<label>)
   │   │   ├─ HTMLLegendElement          (<legend>)
   │   │   ├─ HTMLLinkElement            (<link>)
   │   │   ├─ HTMLMapElement             (<map>)
   │   │   ├─ HTMLMediaElement           (Abstract base class for <audio> and <video>)
   │   │   │   ├─ HTMLAudioElement       (<audio>, already listed above)
   │   │   │   └─ HTMLVideoElement       (<video>)
   │   │   ├─ HTMLMenuElement            (<menu>)
   │   │   ├─ HTMLMetaElement            (<meta>)
   │   │   ├─ HTMLMeterElement           (<meter>)
   │   │   ├─ HTMLModElement             (<ins> / <del>)
   │   │   ├─ HTMLOListElement           (<ol>)
   │   │   ├─ HTMLObjectElement          (<object>)
   │   │   ├─ HTMLOptGroupElement        (<optgroup>)
   │   │   ├─ HTMLOptionElement          (<option>)
   │   │   ├─ HTMLOutputElement          (<output>)
   │   │   ├─ HTMLParagraphElement       (<p>)
   │   │   ├─ HTMLPictureElement         (<picture>)
   │   │   ├─ HTMLPreElement             (<pre>)
   │   │   ├─ HTMLProgressElement        (<progress>)
   │   │   ├─ HTMLQuoteElement           (<blockquote> / <q>)
   │   │   ├─ HTMLScriptElement          (<script>)
   │   │   ├─ HTMLSelectElement          (<select>)
   │   │   ├─ HTMLSourceElement          (<source>)
   │   │   ├─ HTMLSpanElement            (<span>)
   │   │   ├─ HTMLStyleElement           (<style>)
   │   │   ├─ HTMLTableCaptionElement    (<caption>)
   │   │   ├─ HTMLTableCellElement       (<td> / <th>)
   │   │   ├─ HTMLTableColElement        (<col> / <colgroup>)
   │   │   ├─ HTMLTableElement           (<table>)
   │   │   ├─ HTMLTableRowElement        (<tr>)
   │   │   ├─ HTMLTableSectionElement    (<thead> / <tbody> / <tfoot>)
   │   │   ├─ HTMLTemplateElement        (<template>)
   │   │   ├─ HTMLTextAreaElement        (<textarea>)
   │   │   ├─ HTMLTimeElement            (<time>)
   │   │   ├─ HTMLTitleElement           (<title>)
   │   │   ├─ HTMLTrackElement           (<track>)
   │   │   ├─ HTMLUListElement           (<ul>)
   │   │   ├─ HTMLUnknownElement         (Custom or unrecognized tag types by browsers)
   │   │   └─ …(Some browsers may have additional extension interfaces)
   │   │
   │   ├─ SVGElement
   │   │   ├─ SVGAElement                (SVG version of <a>)
   │   │   ├─ SVGCircleElement           (<circle>)
   │   │   ├─ SVGClipPathElement         (<clipPath>)
   │   │   ├─ SVGDefsElement             (<defs>)
   │   │   ├─ SVGDescElement             (<desc>)
   │   │   ├─ SVGEllipseElement          (<ellipse>)
   │   │   ├─ SVGFEBlendElement          (<feBlend>)
   │   │   ├─ SVGFEColorMatrixElement    (<feColorMatrix>)
   │   │   ├─ …                         (Omitted dozens of SVG filter, path, text interfaces)
   │   │   └─ SVGSVGElement              (<svg> top-level container)
   │   │
   │   └─ MathMLElement
   │       ├─ MathMLFractionElement      (<mfrac>)
   │       ├─ MathMLRootElement          (<math>)
   │       ├─ MathMLOperatorElement      (<mo>)
   │       ├─ MathMLIdentifierElement    (<mi>)
   │       └─ …(Various MathML-specific elements)
   │
   ├─ Text                         (Text node, corresponds to Node.TEXT_NODE)
   │
   ├─ Comment                      (Comment node, corresponds to Node.COMMENT_NODE)
   │
   ├─ ProcessingInstruction        (Processing instruction node, corresponds to Node.PROCESSING_INSTRUCTION_NODE)
   │
   ├─ CDATASection                 (Used only in XML/MathML/SVG environments, corresponds to Node.CDATA_SECTION_NODE)
   │
   ├─ Attr                         (Attribute node, corresponds to Node.ATTRIBUTE_NODE; modern usage typically uses getAttribute()/setAttribute())
   │
   └─ DocumentType                 (Node corresponding to <!DOCTYPE>, corresponds to Node.DOCUMENT_TYPE_NODE)
```

---

## III. Review of "Types" at Each Level and Their Meanings

1. **What are the main types under `Node`?**

   - **Document related** (representing the entire document or its fragments):
     - `Document` / `HTMLDocument` / `XMLDocument`
     - `DocumentFragment` / `ShadowRoot`
     - `DocumentType`
   - **Element nodes**: `Element`
   - **Text/comment/processing instructions, etc.**:
     - `Text` (text node)
     - `Comment` (comment node)
     - `ProcessingInstruction` (processing instruction node)
     - `CDATASection` (XML environment only)
     - `Attr` (attribute node)
   - **Obsolete or deprecated nodes**:
     - `EntityReference`, `Notation`, etc. are typically no longer used in modern browsers.

2. **What are the main branches under `Element`?**

   - `HTMLElement`
   - `SVGElement`
   - `MathMLElement`
   - **(Possibly other XML or custom element branches)**

3. **What specific subtypes are under `HTMLElement`?**
   - Basically, **every HTML tag** (e.g., `<div>`, `<span>`, `<input>`, `<form>`, etc.) corresponds to an interface named `HTML…Element`.
   - These interfaces add tag-specific properties and methods on top of `HTMLElement`.
   - Common subtypes include:
     ```
     HTMLAnchorElement, HTMLAreaElement, HTMLAudioElement, HTMLBRElement,
     HTMLBaseElement, HTMLBodyElement, HTMLButtonElement, HTMLCanvasElement,
     HTMLDListElement, HTMLDialogElement, HTMLDivElement, HTMLEmbedElement,
     HTMLFormElement, HTMLHeadingElement, HTMLIFrameElement, HTMLImageElement,
     HTMLInputElement, HTMLLabelElement, HTMLLinkElement, HTMLLIElement,
     HTMLMetaElement, HTMLParagraphElement, HTMLSelectElement, HTMLSpanElement,
     HTMLTableElement, HTMLUListElement, …(dozens more)
     ```

---

### Summary

- **`Node`** is the most universal node interface in the DOM, representing various "nodes" — from the document itself to elements, text, comments, attributes, etc.
- **`Element`** inherits from `Node`, representing "element nodes", including HTML, SVG, MathML, XML, custom tags, etc.
- **`HTMLElement`** inherits from `Element`, specifically corresponding to **HTML tags**, and is the base class for all HTML element interfaces (such as `HTMLDivElement`, `HTMLInputElement`, etc.).

Through the above tree structure, you can intuitively see:

```
EventTarget → Node → Element → (subdivided into SVGElement, MathMLElement, HTMLElement…)
                                               ↓
                                    HTMLAnchorElement, HTMLDivElement, …
```

As well as the parallel existence of `SVGElement` (with dozens of SVG interfaces underneath), `MathMLElement` (MathML interfaces), etc. under `Element`.
