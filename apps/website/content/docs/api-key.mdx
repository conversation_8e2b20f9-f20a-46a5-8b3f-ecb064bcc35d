---
title: Configuring API Key
description: Configure the API key for the Read Frog to start AI translation service
icon: Key
---

## Why Configure an API Key

An API Key is like the “🔑 key” that powers AI—it allows our software to connect to powerful AI translation services.

These AI translation services are provided by vendors such as OpenAI or DeepSeek, so you need to apply for an API Key from them and then enter it into our extension.

## Configuration Method

Click the `Options` button in the popup or floating button to open the extension settings.

![Options](/tutorial/api-key/options.gif)

Enter your OpenAI or DeepSeek API Key.

### How to Obtain a DeepSeek API Key

You can refer to the [official guide](https://api-docs.deepseek.com/) or watch the video below:

<iframe
  src="//player.bilibili.com/player.html?isOutside=true&aid=114340233483711&bvid=BV1JsopY5E3g&cid=29423831641&p=1"
  scrolling="no"
  border="0"
  framespacing="0"
  allowFullScreen="true"
  className="w-full aspect-video"
/>

### How to Obtain an OpenAI API Key

You can refer to the [official guide](https://help.openai.com/en/articles/4936850-where-do-i-find-my-openai-api-key) or watch the video below:

<iframe
  src="https://www.youtube-nocookie.com/embed/SzPE_AE0eEo"
  className="w-full aspect-video"
  allowFullScreen
/>
