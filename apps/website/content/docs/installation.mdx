---
title: Installation
description: A comprehensive guide to using the Read Frog browser extension for immersive language learning
icon: Wrench
---

## User Guide

<Steps>
<Step>

**Install the extension**

Install Read Frog from the store.

</Step>

<Step>

**Configure API key**

Configure [OpenAI](https://openai.com/api/) or [DeepSeek](https://platform.deepseek.com/) API key.

</Step>

<Step>

**Start using**

Find a webpage with an article and start using.

</Step>
</Steps>

### 1. Installation

Currently, Read Frog is available on the [Chrome Web Store](https://chromewebstore.google.com/detail/read-frog/modkelfkcfjpgbfmnbnllalkiogfofhb?utm_source=official) and [Microsoft Edge Add-ons](https://microsoftedge.microsoft.com/addons/detail/read-frog-open-source-a/cbcbomlgikfbdnoaohcjfledcoklcjbo).

<Callout title="Can't access Chrome Web Store?">
  If you cannot access Chrome Web Store in your network environment, we
  recommend you to download and install on [Microsoft Edge
  Add-ons](https://microsoftedge.microsoft.com/addons/detail/read-frog-open-source-a/cbcbomlgikfbdnoaohcjfledcoklcjbo).
  If you don't want to install on Microsoft Edge, you can download and install
  on
  [crxsoso](https://www.crxsoso.com/webstore/detail/modkelfkcfjpgbfmnbnllalkiogfofhb).
</Callout>

### 2. Configuring API Key

The API key is like the key for you to call AI models in the Read Frog. Please refer to [this page](/tutorial/api-key) for setting up the API key.

### 3. Start Using

Find a webpage with an article.

![Article](/tutorial/installation/start-reading.png)

Click the `Read` button and wait for the detailed analysis of the article to appear.

![Explanation](/tutorial/get-started/interface.png)
