---
title: Introduction
description: Transform your web reading into an immersive language learning experience with Read Frog
icon: Album
---

## 🐸 Read Frog - A Language Learning Browser Extension

Read Frog is a browser extension designed for learning languages in depth. The current version can translate and explain the content of article-type web pages, specifically:

1. Immersive Translation: Translate elements directly adjacent to the original elements on the webpage
2. Comprehensive Explanations: Get detailed explanations of words, phrases, and sentences based on your language level

![Page Translation Demo](/tutorial/get-started/page-translation-demo.gif)

![Read Frog Interface](/tutorial/get-started/interface.png)

## Future Plans

The current version is the initial release of Read Frog. Many additional features are planned, including:

1. Translating and explaining only selected portions of web pages
2. Vocabulary notebook for recording new words with context sentences
3. Voice explanations like a real teacher
4. E-book support
5. Mobile support
6. And more...

The core purpose of all our features is to help users learn languages effectively through content they find interesting. Whether it's news, books, or any material you want to understand in depth or keep up with current events.

We need more users centered around this language learning purpose to help us raise questions, suggest features, and co-create the product. We believe the future of Read Frog has limitless possibilities.

## Open Source

Read Frog is open source. You can find the source code on below. Feel free to raise issues and pull requests.

<GithubInfo owner="mengxi-ream" repo="read-frog" />

You can leave us a ⭐ star on Github if you like the project.

## Community

Join our [Discord](https://discord.gg/ej45e3PezJ) community to discuss the project, share your ideas, and get help.
