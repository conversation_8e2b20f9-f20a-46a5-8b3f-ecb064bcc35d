---
title: 安装指南
description: 陪读蛙浏览器扩展的完整使用指南，助你开启沉浸式语言学习之旅
icon: Wrench
---

## 使用指南

<Steps>
<Step>

**安装扩展**

从商店安装陪读蛙。

</Step>

<Step>

**配置 API key**

配置 [OpenAI](https://openai.com/api/) 或 [DeepSeek](https://platform.deepseek.com/) API key。

</Step>

<Step>

**开始使用**

找到一篇包含文章内容的网页并开始使用。

</Step>
</Steps>

### 1. 安装

目前，陪读蛙在 [Chrome Web Store](https://chromewebstore.google.com/detail/read-frog/modkelfkcfjpgbfmnbnllalkiogfofhb?utm_source=official) 和 [Microsoft Edge Add-ons](https://microsoftedge.microsoft.com/addons/detail/read-frog-open-source-a/cbcbomlgikfbdnoaohcjfledcoklcjbo) 上提供。

<Callout title="无法访问 Chrome Web Store？">
  如果你无法访问 Chrome Web Store，我们推荐你下载并安装在 [Microsoft Edge
  Add-ons](https://microsoftedge.microsoft.com/addons/detail/read-frog-open-source-a/cbcbomlgikfbdnoaohcjfledcoklcjbo)。
  如果你不想安装在 Microsoft Edge，你可以在
  [crxsoso](https://www.crxsoso.com/webstore/detail/modkelfkcfjpgbfmnbnllalkiogfofhb)
  下载并安装。
  [crxsoso](https://www.crxsoso.com/webstore/detail/modkelfkcfjpgbfmnbnllalkiogfofhb).
</Callout>

### 2. 配置 API Key

API Key 就像你在陪读蛙中调用 AI 模型的钥匙。请参考[本页](/tutorial/api-key)设置 API Key。

### 3. 开始使用

找到一篇包含文章内容的网页。

![文章](/tutorial/installation/start-reading.png)

点击 `阅读` 按钮，等待文章详细分析结果出现。

![解释](/tutorial/get-started/interface.png)
