# @read-frog/website

## 1.0.2

### Patch Changes

- [#252](https://github.com/mengxi-ream/read-frog/pull/252) [`2756fee`](https://github.com/mengxi-ream/read-frog/commit/2756feeaf3ffcd5e7d367882e18896a8ffa4d4ba) Thanks [@chandanrakholia](https://github.com/chandanrakholia)! - fix: no login avatar on mobile home page after login

- [#316](https://github.com/mengxi-ream/read-frog/pull/316) [`17409f6`](https://github.com/mengxi-ream/read-frog/commit/17409f6f9fc36882257b850007569b665da7b31a) Thanks [@sedationh](https://github.com/sedationh)! - add conteibution guide about dev

## 1.0.1

### Patch Changes

- [#273](https://github.com/mengxi-ream/read-frog/pull/273) [`12624be`](https://github.com/mengxi-ream/read-frog/commit/12624be6c5cbfcc9097a9c5c2c519a74e12a055f) Thanks [@taiiiyang](https://github.com/taiiiyang)! - refactor: extract ui, themes and cn to @repo/ui

## 1.0.0

### Major Changes

- [#232](https://github.com/mengxi-ream/read-frog/pull/232) [`c5c062e`](https://github.com/mengxi-ream/read-frog/commit/c5c062ea0ff71c6e0b96396e780406a4a1de18b5) Thanks [@ananaBMaster](https://github.com/ananaBMaster)! - feat: integrate trpc

### Minor Changes

- [#185](https://github.com/mengxi-ream/read-frog/pull/185) [`8476a1a`](https://github.com/mengxi-ream/read-frog/commit/8476a1af1c6f73813eb9e82df4e5a5fd406fd56e) Thanks [@mengxi-ream](https://github.com/mengxi-ream)! - integrate better auth for testing

- [`3c745d2`](https://github.com/mengxi-ream/read-frog/commit/3c745d2b10af534119066b9627edeaeefe3bc9e6) Thanks [@mengxi-ream](https://github.com/mengxi-ream)! - feat: save vocabulary

- [#187](https://github.com/mengxi-ream/read-frog/pull/187) [`0f6d20a`](https://github.com/mengxi-ream/read-frog/commit/0f6d20aff5ff23557bd880ab5eabc4765268c969) Thanks [@mengxi-ream](https://github.com/mengxi-ream)! - feat: add google oauth login

### Patch Changes

- [#203](https://github.com/mengxi-ream/read-frog/pull/203) [`5a60dfa`](https://github.com/mengxi-ream/read-frog/commit/5a60dfa4f37e9c1c87dfbe0363dcc62daf3951ee) Thanks [@Andrew-Tan](https://github.com/Andrew-Tan)! - docs: contribution guide update

- [#230](https://github.com/mengxi-ream/read-frog/pull/230) [`8f80ba2`](https://github.com/mengxi-ream/read-frog/commit/8f80ba22583e9f9641181050fb2be125305f27d1) Thanks [@mengxi-ream](https://github.com/mengxi-ream)! - fix: ssr load tabler icons in nextjs

- [#244](https://github.com/mengxi-ream/read-frog/pull/244) [`d389488`](https://github.com/mengxi-ream/read-frog/commit/d38948856434a06709e381d8b61cf6e3d1e50329) Thanks [@sedationh](https://github.com/sedationh)! - chore: update example environment files

- [#228](https://github.com/mengxi-ream/read-frog/pull/228) [`3e4f885`](https://github.com/mengxi-ream/read-frog/commit/3e4f8850507dad971fed84143a658220ab33b124) Thanks [@mengxi-ream](https://github.com/mengxi-ream)! - style: change more icons to iconify

- [#191](https://github.com/mengxi-ream/read-frog/pull/191) [`31f816f`](https://github.com/mengxi-ream/read-frog/commit/31f816fbd8b69a1a4781dc2210636344a11144b8) Thanks [@mengxi-ream](https://github.com/mengxi-ream)! - fix: github token in GithubInfo

## 0.1.2

### Patch Changes

- [#152](https://github.com/mengxi-ream/read-frog/pull/152) [`0934d69`](https://github.com/mengxi-ream/read-frog/commit/0934d69dba3ec7fdb4d048d006590e23c6057007) Thanks [@mengxi-ream](https://github.com/mengxi-ream)! - docs: update contribution guide

- [#156](https://github.com/mengxi-ream/read-frog/pull/156) [`c795431`](https://github.com/mengxi-ream/read-frog/commit/c795431ad4b8091fcb511afd7b79eda68d384200) Thanks [@taiiiyang](https://github.com/taiiiyang)! - feat: support markdown file export; fix scroll style in side.content

  style: optimize ui in guide page

## 0.1.1

### Patch Changes

- [#151](https://github.com/mengxi-ream/read-frog/pull/151) [`be7bea5`](https://github.com/mengxi-ream/read-frog/commit/be7bea57004e0db349bde5245fd7bc30f2fb8baf) Thanks [@taiiiyang](https://github.com/taiiiyang)! - docs: optimize contribution docs

## 0.1.0

### Minor Changes

- [#137](https://github.com/mengxi-ream/read-frog/pull/137) [`307f672`](https://github.com/mengxi-ream/read-frog/commit/307f672a26b600b2b765c3d3612c440d71908027) Thanks [@mengxi-ream](https://github.com/mengxi-ream)! - chore: move the website code to this monorepo
