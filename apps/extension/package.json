{"name": "@read-frog/extension", "type": "module", "version": "1.3.0", "private": true, "description": "Read Frog browser extension for language learning", "scripts": {"build": "wxt build", "build:firefox": "wxt build -b firefox", "dev": "wxt", "dev:edge": "wxt -b edge", "dev:firefox": "wxt -b firefox", "lint": "eslint", "lint:fix": "eslint --fix", "postinstall": "wxt prepare", "test": "vitest run", "test:cov": "vitest run --coverage", "test:watch": "vitest", "type-check": "tsc --noEmit", "zip": "wxt zip", "zip:firefox": "wxt zip -b firefox"}, "dependencies": {"@ai-sdk/deepseek": "^1.0.7", "@ai-sdk/google": "^2.0.6", "@ai-sdk/openai": "^2.0.12", "@ai-sdk/react": "^2.0.12", "@mozilla/readability": "^0.6.0", "@repo/api": "workspace:*", "@repo/definitions": "workspace:*", "@repo/ui": "workspace:*", "@tabler/icons-react": "^3.34.0", "@tanstack/react-query": "^5.84.2", "@tanstack/react-query-devtools": "^5.84.2", "@trpc/client": "^11.4.4", "@trpc/tanstack-react-query": "^11.4.4", "@webext-core/messaging": "^2.2.0", "@wxt-dev/i18n": "^0.2.4", "ai": "^5.0.12", "better-auth": "^1.2.12", "case-anything": "^3.1.2", "class-variance-authority": "^0.7.1", "crypto-js": "^4.2.0", "deepmerge": "^4.3.1", "dexie": "^4.0.11", "file-saver": "^2.0.5", "franc-min": "^6.2.0", "hotkeys-js": "^3.13.10", "jotai": "^2.12.3", "react": "^19.1.1", "react-dom": "^19.1.1", "react-router": "^7.6.0", "sonner": "^2.0.7", "superjson": "^2.2.2", "zod": "^4.0.17"}, "devDependencies": {"@iconify/react": "^6.0.0", "@repo/eslint-config": "workspace:*", "@tailwindcss/postcss": "^4.1.5", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/crypto-js": "^4.2.2", "@types/file-saver": "^2.0.7", "@types/jest": "^29.5.14", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react": "^4.4.1", "@vitest/coverage-istanbul": "^3.1.3", "@wxt-dev/module-react": "^1.1.3", "autoprefixer": "^10.4.21", "eruda": "^3.4.1", "jsdom": "^26.1.0", "postcss": "^8.5.3", "postcss-rem-to-responsive-pixel": "^6.0.2", "tailwindcss": "^4.1.5", "typescript": "^5.8.3", "vitest": "^3.1.3", "wxt": "^0.20.7"}}