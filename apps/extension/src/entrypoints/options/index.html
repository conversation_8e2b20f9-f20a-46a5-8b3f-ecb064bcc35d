<!doctype html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Options | Read Frog</title>

    <!-- Customize the manifest options -->
    <meta name="manifest.open_in_tab" content="true" />
    <!-- <meta name="manifest.browser_style" content="true" /> -->

    <!-- Set include/exclude if the page should be removed from some builds -->
    <!-- <meta
      name="manifest.include"
      content="['chrome', 'firefox', 'edge', 'safari']"
    /> -->
    <!-- <meta name="manifest.exclude" content="['chrome']" /> -->
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="./main.tsx"></script>
  </body>
</html>
