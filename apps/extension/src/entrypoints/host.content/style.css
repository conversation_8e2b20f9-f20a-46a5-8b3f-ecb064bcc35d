@import '@/assets/tailwind/custom-translation-node.css';
@import '@/assets/tailwind/host-theme.css';

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.read-frog-spinner {
  border: 3px solid var(--read-frog-muted); /* 轨迹灰色 */
  border-top: 3px solid var(--read-frog-primary); /* 主色调，首次翻译蓝色、缓存应改为绿色 */
  border-radius: 50%;
  width: 6px;
  height: 6px;
  margin: 0 4px;
  animation: spin 1s linear infinite;
  display: inline-block;
  vertical-align: middle; /* 添加垂直居中对齐 */
  box-sizing: content-box; /* 确保在所有网站上使用相同的盒模型 */
}

.read-frog-translated-content-wrapper {
  word-break: break-word;
  user-select: text;
}

.read-frog-translated-block-content {
  display: inline-block;
  margin: 8px 0 !important; /* 上下边距设置为8px，并强制覆盖其他样式 */
  color: inherit; /* 保持文本颜色与周围文本一致 */
  font-family: inherit;
}

.read-frog-translated-inline-content {
  display: inline;
  color: inherit;
  font-family: inherit;
  text-decoration: inherit;
}
