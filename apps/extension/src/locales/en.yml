name: Read Frog
extName: Read Frog - Open Source Immersive Translate
extDescription: Read Frog is an open source browser extension designed to help you learn languages deeply from any website.
popup:
  autoLang: Auto
  sourceLang: Source
  targetLang: Target
  read: Read Article
  translate: Translate
  showOriginal: Show Original
  enabledFloatingButton: Enable floating button
  enabledSelectionToolbar: Enable selection toolbar
  alwaysTranslate: Always translate this site
  options: Options
  hover: Hover
  translateParagraph: to translate paragraph
  help:
    title: Help
    description: Discover more tips and tricks
  book:
    title: E-book
    description: Read any ePub books on Neat Reader. (Neat Reader and Read Frog are independent; purchases in Neat Reader have no connection to Read Frog.)
  discord:
    title: Discord
    description: Join Discord server to connect with developers and other users
  github:
    description: ⭐️ Star us for new release notifications without delay!
options:
  setAPIKeyWarning:
    please: Please set API Key on
    page: page
  apiProviders:
    title: API Providers
    description:
      openai: Provides models like GPT-4o
      deepseek: Recommend to use in China
      openrouter: The unified interface for multiple LLM providers
      ollama: Local LLM
      gemini: Provide models such as Gemini-2.5-Pro
      deeplx: Unofficial DeepL API
    apiKey:
      showAPIKey: Show API Key
    advancedConfig:
      show: Show Advanced Config
      hide: Hide Advanced Config
  general:
    title: General
    readConfig:
      title: Read Config
      description:
        For all advanced AI functions, such as in-depth article explanations and keyword extraction.
      provider: Provider
      model:
        title: Model
        enterCustomModel: Enter the name of the custom model
    translationConfig:
      title: Translation Config
      description: Only for translation within web pages, such as bilingual paragraph translation.
      provider: Provider
      model:
        title: Model
        enterCustomModel: Enter the name of the custom model
      translateRange:
        title: Translate Range
        range:
          main: Main Content
          all: All Content
    resetConfig:
      title: Reset Config
      description: Reset all config to default value
      dialog:
        title: Reset Config
        trigger: Reset Config
        confirm: Confirm
        cancel: Cancel
        description: This operation will clear all your custom configurations and reset them to default values. Please confirm.
  translation:
    title: Translation
    requestQueueConfig:
      title: Translation Rate
      firstOnDescription: Adjust the processing speed of translation requests using the
      lastOnDescription: algorithm
      capacity:
        title: Maximum Burst Request Count
        description: The allowed temporary burst request count determines the maximum number of requests that can be processed concurrently within a short period
      rate:
        title: Average Requests Per Second
        description: Average requests per second, but allows configuring a maximum burst count to temporarily send excess requests
    personalizedPrompt:
      title: Personalized Prompt
      description: Customizing different types of Prompts allows you to adjust the Prompt type yourself during translation. A Prompt represents the text that the user sends to the LLM, where {{input}} denotes the text content of the paragraph and {{targetLang}} denotes the target language.
      addPrompt: Add Prompt
      default: Default
      importSuccess: Import Success
      import: Import
      export: Export
      editPrompt:
        title: Edit Prompt
        name: Name
        save: Save Changes
        close: Cancel
      deletePrompt:
        title: Delete Prompt
        description: Confirm delete prompt
        confirm: Confirm
        cancel: Cancel
    translationStyle:
      title: Translation Display Style
      description: Distinguish display styles for translation results (see sample for specific style demonstrations)
      style:
        default: None
        blur: Blur Effect
        blockquote: Blockquote
        weakened: Weakened
side:
  sourceLang: Source Language
  targetLang: Target Language
  fileExport: File Export
readService:
  title: Read Service
  description: For read and explain articles
translateService:
  title: Translation Service
  description: For translate the webpage only
  normalTranslator: Normal Translator
  aiTranslator: AI Translator
translatePrompt:
  title: AI Translation Style
  description: Select a Prompt to use during translation. Add or modify custom Prompts on the options page.
languageLevel: Your Source Lang Level
languageLevels:
  beginner: Beginner
  intermediate: Intermediate
  advanced: Advanced
noAPIKeyConfig:
  warning: If you want to use AI models, please set API key on the options page first
  warningWithLink:
    youMust: You must
    setTheAPIKey: set the API key
    firstOnThe: first on the
    optionsPage: options
    page: page
translation:
  sameLanguage: Source and target languages must be different
  autoModeSameLanguage: Detected language '$1' is the same as target language
