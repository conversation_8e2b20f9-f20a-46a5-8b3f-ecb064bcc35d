name: 陪读蛙
extName: 陪读蛙 - 开源沉浸式翻译
extDescription: 陪读蛙是一款开放源代码的浏览器插件，旨在帮助您从任何网站深入学习语言。
popup:
  autoLang: 自动检测
  sourceLang: 来源语言
  targetLang: 目标语言
  read: 阅读文章
  translate: 翻译
  showOriginal: 显示原文
  enabledFloatingButton: 启用悬浮按钮
  enabledSelectionToolbar: 启用选择工具栏
  alwaysTranslate: 总是翻译这个网站
  options: 选项
  hover: 鼠标悬停
  translateParagraph: 来翻译段落
  help:
    title: 帮助
    description: 发现更多提示和技巧
  book:
    title: 电子书
    description: 阅读 ePub 电子书。（Neat Reader 和陪读蛙是独立的产品；在 Neat Reader 中的购买与陪读蛙无关。）
  discord:
    title: Discord
    description: 加入 Discord 服务器，与开发者和其他用户交流
  github:
    description: ⭐️ 为我们加星，获取及时 GitHub 发布通知！
options:
  setAPIKeyWarning:
    please: 请在
    page: 页设置 API Key
  apiProviders:
    title: API 提供商
    description:
      openai: 提供 GPT-4o 等模型
      deepseek: 推荐在中国使用
      openrouter: 多个 LLM 提供商的统一接口
      ollama: 本地大模型服务
      gemini: 提供 gemini-2.5-pro 等模型
      deeplx: 非官方 DeepL API
    apiKey:
      showAPIKey: 显示 API Key
    advancedConfig:
      show: 显示高级配置
      hide: 隐藏高级配置
  general:
    title: 通用
    readConfig:
      title: 阅读配置
      description:
        适用于所有高级 AI 功能，如深度文章解释和关键词提取。
      provider: 提供商
      model:
        title: 模型
        enterCustomModel: 输入自定义模型名称
    translationConfig:
      title: 翻译配置
      description: 仅用于网页内的翻译，如双语段落翻译。
      provider: 提供商
      model:
        title: 模型
        enterCustomModel: 输入自定义模型名称
      translateRange:
        title: 翻译范围
        range:
          main: 主要内容
          all: 所有内容
    resetConfig:
      title: 恢复默认配置
      description:
        将所有配置重置为默认
      dialog:
        title: 恢复默认配置
        trigger: 重置配置
        confirm: 确认
        cancel: 取消
        description: 该操作将清空您的所有自定义配置，并将其重置为默认值，请确认是否清除。
  translation:
    title: 翻译
    requestQueueConfig:
      title: 翻译速率
      firstOnDescription: 调整翻译请求的处理速度，使用
      lastOnDescription: 实现
      capacity:
        title: 最大突发请求数
        description: 允许的暂时突发请求数量，决定了在短时间内最多能同时处理多少个请求
      rate:
        title: 每秒平均请求数量
        description: 平均每秒的请求数量，不过允许配置最大突发请求数来短暂发送超量请求。
    personalizedPrompt:
      title: 个性化 Prompt
      description: 自定义不同类型的 Prompt，可以在翻译时自行调整 Prompt 类型。Prompt 表示以用户身份发送给 llm 的文本，其中 {{input}} 表示段落的文本内容，{{targetLang}} 表示目标语言。
      addPrompt: 添加 Prompt
      default: 通用
      importSuccess: 导入成功
      import: 导入
      export: 导出
      editPrompt:
        title: 编辑 Prompt
        name: 标题
        save: 保存
        close: 取消
      deletePrompt:
        title: 删除 Prompt
        description: 确认删除该 Prompt
        confirm: 确认
        cancel: 取消
    translationStyle:
      title: 翻译显示样式
      description: 区分翻译结果的显示样式，具体展示样式可参考示例
      style:
        default: 无
        blur: 模糊效果
        blockquote: 引用
        weakened: 弱化
side:
  sourceLang: 来源语言
  targetLang: 目标语言
  fileExport: 文件导出
readService:
  title: 阅读服务
  description: 用于阅读和解释文章
translateService:
  title: 翻译服务
  description: 仅用于翻译网页内容
  normalTranslator: 普通翻译
  aiTranslator: AI 翻译
translatePrompt:
  title: AI 翻译风格
  description: 选择在翻译时使用的 Prompt，可在选项页新增或修改自定义 Prompt
languageLevel: 你的来源语言水平
languageLevels:
  beginner: 初级
  intermediate: 中级
  advanced: 高级
noAPIKeyConfig:
  warning: 如果想要使用 AI 模型，请先在选项页设置 API Key
  warningWithLink:
    youMust: 您必须先
    setTheAPIKey: 设置 API Key
    firstOnThe: 在
    optionsPage: 选项
    page: 页
translation:
  sameLanguage: 源语言和目标语言必须不同
  autoModeSameLanguage: 检测到的语言 '$1' 与目标语言相同
