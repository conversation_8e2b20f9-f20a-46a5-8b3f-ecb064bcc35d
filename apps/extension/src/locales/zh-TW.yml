name: 陪讀蛙
extName: 陪讀蛙 - 開源沉浸式翻譯
extDescription: 陪讀蛙是一款開放源代碼的瀏覽器插件，旨在幫助您從任何網站深入學習語言。
popup:
  autoLang: 自動偵測
  sourceLang: 來源語言
  targetLang: 目標語言
  read: 閱讀文章
  translate: 翻譯
  showOriginal: 顯示原文
  enabledFloatingButton: 啟用懸浮按鈕
  enabledSelectionToolbar: 啟用選擇工具列
  alwaysTranslate: 總是翻譯這個網站
  options: 選項
  hover: 滑鼠懸停
  translateParagraph: 來翻譯段落
  help:
    title: 幫助
    description: 發現更多提示和技巧
  book:
    title: 電子書
    description: 閱讀 ePub 電子書。（Neat Reader 和陪讀蛙是獨立的產品；在 Neat Reader 中的購買與陪讀蛙無關。）
  discord:
    title: Discord
    description: 加入 Discord 服務器，與開發者和其他用戶交流
  github:
    description: ⭐️ 為我們加星，獲取及時 GitHub 發布通知！
options:
  setAPIKeyWarning:
    please: 請在
    page: 頁設置 API Key
  apiProviders:
    title: API 提供商
    description:
      openai: 提供 GPT-4o 等模型
      deepseek: 推薦在中國使用
      openrouter: 多個 LLM 提供商的統一接口
      ollama: 本地大模型服務
      gemini: 提供 Gemini-2.5-Pro 等模型
      deeplx: 非官方 DeepL API
    apiKey:
      showAPIKey: 顯示 API Key
    advancedConfig:
      show: 顯示高級配置
      hide: 隱藏高級配置
  general:
    title: 通用
    readConfig:
      title: 閱讀配置
      description:
        適用於所有高級 AI 功能，如深度文章解釋和關鍵詞提取。
      provider: 提供商
      model:
        title: 模型
        enterCustomModel: 輸入自定義模型名稱
    translationConfig:
      title: 翻譯配置
      description: 僅用於網頁內的翻譯，如雙語段落翻譯。
      provider: 提供商
      model:
        title: 模型
        enterCustomModel: 輸入自定義模型名稱
      translateRange:
        title: 翻譯範圍
        range:
          main: 主要內容
          all: 所有內容
    resetConfig:
      title: 重置配置
      description: 重置所有配置為預設值
      dialog:
        title: 重置配置
        trigger: 重置配置
        confirm: 確認
        cancel: 取消
        description: 此操作將清空您的所有自定義配置，並將其重置為預設值，請確認是否清除。
  translation:
    title: 翻譯
    requestQueueConfig:
      title: 翻譯速率
      firstOnDescription: 調整翻譯請求的處理速度，使用
      lastOnDescription: 實現
      capacity:
        title: 最大突發請求數
        description: 允許的暫時突發請求數量，決定了在短時間內最多能同時處理多少個請求
      rate:
        title: 每秒平均請求數量
        description: 平均每秒的請求數量，但允許設定最大突發請求數來短暫發送超量請求
    personalizedPrompt:
      title: 個人化 Prompt
      description: 自訂不同類型的 Prompt，可以在翻譯時自行調整 Prompt 類型。Prompt 表示以用戶身份發送給 LLM 的文字，其中 {{input}} 表示段落的文字內容，{{targetLang}} 表示目標語言。
      addPrompt: 添加 Prompt
      default: 通用
      importSuccess: 導入成功
      import: 導入
      export: 導出
      editPrompt:
        title: 編輯 Prompt
        name: 標題
        save: 保存
        close: 取消
      deletePrompt:
        title: 删除 Prompt
        description: 確定要刪除 Prompt 嗎
        confirm: 確定
        cancel: 取消
    translationStyle:
      title: 翻譯顯示樣式
      description: 區分翻譯結果的顯示樣式（具體展示樣式可參照範例）
      style:
        default: 無
        blur: 模糊效果
        blockquote: 引用
        weakened: 弱化
side:
  sourceLang: 來源語言
  targetLang: 目標語言
  fileExport: 文件導出
readService:
  title: 閱讀服務
  description: 用於閱讀和解釋文章
translateService:
  title: 翻譯服務
  description: 僅用於翻譯網頁內容
  normalTranslator: 普通翻譯
  aiTranslator: AI 翻譯
translatePrompt:
  title: AI 翻譯風格
  description: 選擇在翻譯時使用的 Prompt。可在選項頁面新增或修改自訂 Prompt
languageLevel: 你的來源語言水平
languageLevels:
  beginner: 初級
  intermediate: 中級
  advanced: 高級
noAPIKeyConfig:
  warning: 如果想要使用 AI 模型，請先在選項頁設置 API Key
  warningWithLink:
    youMust: 您必須先
    setTheAPIKey: 設置 API Key
    firstOnThe: 在
    optionsPage: 選項
    page: 頁
translation:
  sameLanguage: 源語言和目標語言必須不同
  autoModeSameLanguage: 檢測到的語言 '$1' 與目標語言相同
