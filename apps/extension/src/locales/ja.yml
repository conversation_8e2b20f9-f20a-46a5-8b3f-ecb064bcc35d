name: 読書カエル
extName: 読書カエル - オープンソースインビジブル翻訳
extDescription: 読書カエルは、あらゆるウェブサイトから言語を深く学ぶために設計されたオープンソースのブラウザ拡張機能です。
popup:
  autoLang: 自動検出
  sourceLang: ソース言語
  targetLang: 翻訳言語
  read: 記事を読む
  translate: 翻訳
  showOriginal: 原文を表示
  enabledFloatingButton: フローティングボタンを有効化
  enabledSelectionToolbar: 選択ツールバーを有効化
  alwaysTranslate: このサイトを常に翻訳する
  options: オプション
  hover: ホバー
  translateParagraph: 段落を翻訳
  help:
    title: ヘルプ
    description: より多くのヒントとトリックを発見します
  book:
    title: 電子書籍
    description: 任意のePub書籍をAIで読み解きます。（Neat Reader と Read Frog は独立した製品です。Neat Reader での購入は Read Frog とは関係ありません。）
  discord:
    title: Discord
    description: 開発者や他のユーザーとつながる
  github:
    description: ⭐️ スターを付けると、新しいリリース通知をすぐに受け取れます！
options:
  setAPIKeyWarning:
    please: 設定ページで
    page: APIキーを設定してください
  apiProviders:
    title: APIプロバイダー
    description:
      openai: GPT-4oなどのモデルを提供
      deepseek: 中国国内での使用を推奨
      openrouter: 複数のLLMプロバイダを統合するインターフェース
      ollama: ローカル大規模言語モデルサービス
      gemini: Gemini-2.5-Pro などのモデルを提供する
      deeplx: 非公式 DeepL API
    apiKey:
      showAPIKey: APIキーを表示
    advancedConfig:
      show: 詳細設定を表示
      hide: 詳細設定を非表示
  general:
    title: 一般
    readConfig:
      title: 読解設定
      description: 高度なAI機能（記事の詳細解説、キーワード抽出など）に使用されます。
      provider: プロバイダー
      model:
        title: モデル
        enterCustomModel: カスタムモデル名を入力
    translationConfig:
      title: 翻訳設定
      description: ウェブページ内の翻訳にのみ使用されます（バイリンガル段落翻訳など）。
      provider: プロバイダー
      model:
        title: モデル
        enterCustomModel: カスタムモデル名を入力
      translateRange:
        title: 翻訳範囲
        range:
          main: メインコンテンツ
          all: すべてのコンテンツ
    resetConfig:
      title: 設定をリセット
      description: すべての設定をデフォルト値にリセットします。
      dialog:
        title: 設定をリセット
        trigger: 設定をリセット
        confirm: 確認
        cancel: キャンセル
        description: この操作は、すべての設定をデフォルト値にリセットします。ご確認ください。
  translation:
    title: 翻訳
    requestQueueConfig:
      title: 翻訳速度
      firstOnDescription: 翻訳リクエストの処理速度を調整し、
      lastOnDescription: を利用して実装する
      capacity:
        title: 最大バーストリクエスト数
        description: 許容される一時的バーストリクエスト数は、短期間内に同時処理可能な最大リクエスト数を決定する
      rate:
        title: 秒間平均リクエスト数
        description: 平均秒間リクエスト数（ただし、最大バーストリクエスト数を設定し一時的な超過リクエスト送信を許可）
    personalizedPrompt:
      title: 個人化プロンプト
      description: 異なるタイプのプロンプトをカスタマイズすることで、翻訳時にプロンプトタイプを自ら調整できます。プロンプトとは、ユーザーがLLMに送信するテキストを指し、その中で {{input}} は段落のテキスト内容を、{{targetLang}} はターゲット言語を表します。
      addPrompt: プロンプトを追加
      default: デフォルト
      importSuccess: インポート成功
      import: インポート
      export: エクスポート
      editPrompt:
        title: プロンプトを編集
        name: 名前
        save: 保存
        close: キャンセル
      deletePrompt:
        title: プロンプトを削除
        description: このプロンプトを削除しますか
        confirm: 確認
    translationStyle:
      title: 翻訳表示スタイル
      description: 翻訳結果の表示スタイルを区別（具体的な表示例はサンプルを参照）
      style:
        default: なし
        blur: ぼかし効果
        blockquote: 引用
        weakened: 弱化
side:
  sourceLang: ソース言語
  targetLang: 翻訳言語
  fileExport: ファイルエクスポート
readService:
  title: 読解サービス
  description: 記事の読解と解説に使用
translateService:
  title: 翻訳サービス
  description: ウェブページの翻訳にのみ使用
  normalTranslator: 通常翻訳
  aiTranslator: AI翻訳
translatePrompt:
  title: AI翻訳スタイル
  description: 翻訳時に使用するプロンプトを選択できます。オプションページでカスタムプロンプトを追加または変更できます
languageLevel: ソース言語のレベル
languageLevels:
  beginner: 初級
  intermediate: 中級
  advanced: 上級
noAPIKeyConfig:
  warning: AIモデルを使用するには、まずオプションページでAPIキーを設定してください
  warningWithLink:
    youMust: まず
    setTheAPIKey: APIキーを設定
    firstOnThe: する必要があります（
    optionsPage: オプションページ
    page: ）
translation:
  sameLanguage: ソース言語とターゲット言語は異なる必要があります
  autoModeSameLanguage: 検出された言語 '$1' はターゲット言語と同じです
