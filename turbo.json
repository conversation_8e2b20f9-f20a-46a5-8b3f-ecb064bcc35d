{"$schema": "https://turborepo.com/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"]}, "dev": {"cache": false, "persistent": true, "env": ["SKIP_ENV_VALIDATION"]}, "dev:edge": {"cache": false, "persistent": true, "env": ["SKIP_ENV_VALIDATION"]}, "lint": {"dependsOn": ["^build"]}, "lint:fix": {"dependsOn": ["^build"], "cache": false}, "test": {"dependsOn": ["^build"]}, "type-check": {"dependsOn": ["^build"]}}}