{"name": "read-frog", "type": "module", "version": "0.0.0", "private": true, "packageManager": "pnpm@10.14.0", "description": "Read Frog monorepo - browser extension and website for language learning", "author": "MengXi <<EMAIL>> (https://mengxi.work)", "scripts": {"build": "turbo build", "db:generate": "turbo db:generate", "db:migrate": "turbo db:migrate", "db:studio": "turbo db:studio", "dev": "turbo dev", "dev:edge": "turbo dev:edge", "dev:extension": "turbo dev --filter=@read-frog/extension", "dev:website": "turbo dev --filter=@read-frog/website", "lint": "eslint . && turbo lint", "lint:fix": "eslint . --fix && turbo lint:fix", "syncpack:lint": "syncpack lint", "syncpack:fix": "syncpack fix-mismatches", "prepare": "husky", "release": "changeset tag && git push origin --tags", "test": "turbo test", "type-check": "turbo type-check", "zip": "turbo zip"}, "devDependencies": {"@changesets/changelog-github": "^0.5.1", "@changesets/cli": "^2.29.3", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@repo/eslint-config": "workspace:*", "eslint": "^9.32.0", "husky": "^9.1.7", "lint-staged": "^16.0.0", "syncpack": "^13.0.4", "turbo": "^2.5.5", "typescript": "^5.8.3"}, "devEngines": {"runtime": {"name": "node", "version": "^22.18.0", "onFail": "download"}}}