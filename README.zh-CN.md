<div align="center"><a name="readme-top"></a>

[![][image-banner]][website]

一款开源的AI驱动的浏览器语言学习扩展。<br/>
支持沉浸式翻译、文章分析、多种AI模型等功能。<br/>
在浏览器中利用AI轻松深入地掌握语言。

[English](./README.md) | **简体中文** | [官方网站](https://readfrog.app)

<!-- SHIELD GROUP -->

[![][extension-release-shield]][github-release-link]
[![][chrome-version-shield]][chrome-store-link]
[![][edge-version-shield]][edge-store-link]
[![][website-release-shield]][github-release-link]<br/>
[![][discord-shield]][discord-link]
[![][chrome-users-shield]][chrome-store-link]
[![][edge-users-shield]][edge-store-link]<br/>
[![][star-history-shield]][star-history-link]
[![][contributors-shield]][contributors-link]
![][last-commit-shield]
[![][issues-shield]][issues-link]<br/>
[![][sponsor-shield]][sponsor-link]

</div>

<details>
<summary><kbd>目录</kbd></summary>

#### 目录

- [📺 演示](#-演示)
- [👋🏻 快速开始 \& 加入我们的社区](#-快速开始--加入我们的社区)
  - [下载](#下载)
  - [社区](#社区)
- [✨ 功能](#-功能)
- [🤝 贡献](#-贡献)
  - [贡献代码](#贡献代码)
- [❤️ 赞助者](#️-赞助者)

<br/>

</details>

> [!NOTE]
>
> **🚀 v1.0 即将到来！** 下一个主要版本将带来令人兴奋的新功能：
>
> - 📚 **生词本** - 保存和复习您学过的单词
> - ✨ **划词翻译** - 只需选择文本即可翻译
> - 🤖 **扩展AI支持** - 包括 Gemini 和更多语言模型
>
> 敬请期待迄今为止最大的更新！

## 📺 演示

![Read Frog](/assets/read-demo.gif)

<div align="center">
  <img src="assets/node-translation-demo.gif" width="38%" alt="Read Frog 弹窗界面" />
  <img src="assets/page-translation-demo.gif" width="60%" alt="Read Frog 翻译界面" />
</div>

## 👋🏻 快速开始 & 加入我们的社区

Read Frog 的愿景是为各个级别的语言学习者提供易于使用、智能化和个性化的语言学习体验。这在AI时代已成为可能，但市场上很少有产品满足这一需求。因此，我们决定自己动手，最终让世界不再依赖人类语言教师。

无论您是用户还是开发者，Read Frog 都将是您实现这一愿景的方式。请注意，Read Frog 目前正在积极开发中，欢迎对遇到的任何[问题][issues-link]提供反馈。

### 下载

| 浏览器 | 版本                                            | 下载                                                             |
| ------ | ----------------------------------------------- | ---------------------------------------------------------------- |
| Chrome | [![][chrome-version-shield]][chrome-store-link] | [Chrome 应用商店][chrome-store-link] 或 [中国镜像][crxsoso-link] |
| Edge   | [![][edge-version-shield]][edge-store-link]     | [Microsoft Edge 插件商店][edge-store-link]                       |

### 社区

| [![][discord-shield-badge]][discord-link] | 在 Discord 中提问，与开发者交流。              |
| :---------------------------------------- | :--------------------------------------------- |
| [![][wechat-shield-badge]][wechat-link]   | 如果您在中国大陆，可以添加微信账号加入微信群。 |

> \[!IMPORTANT]
>
> **⭐️ 给我们点星**, 您将及时收到来自 GitHub 的所有发布通知 \~

[![][image-star]][github-star-link]

<details>
<summary>
  <kbd>Star 历史</kbd>
</summary>

<a href="https://www.star-history.com/#mengxi-ream/read-frog&Timeline">
 <picture>
   <source media="(prefers-color-scheme: dark)" srcset="https://api.star-history.com/svg?repos=mengxi-ream/read-frog&type=Timeline&theme=dark" />
   <source media="(prefers-color-scheme: light)" srcset="https://api.star-history.com/svg?repos=mengxi-ream/read-frog&type=Timeline" />
   <img alt="Star History Chart" src="https://api.star-history.com/svg?repos=mengxi-ream/read-frog&type=Timeline" />
 </picture>
</a>

</details>

<div align="right">

[![][back-to-top]](#readme-top)

</div>

## ✨ 功能

将您的日常网络阅读转变为沉浸式语言学习之旅！Read Frog 是您的智能伴侣，让语言学习变得自然而愉快。

- 沉浸式翻译：直接在网页原始元素旁边翻译内容
- 智能内容提取：自动识别并提取文章的主要内容
- 全面解释：根据您的语言水平提供详细的单词、短语和句子解释
- 情境学习：通过您真正感兴趣的内容学习语言
- 多种AI模型：支持 OpenAI、DeepSeek，未来将支持更多

无论您在阅读新闻、文章还是任何网络内容，Read Frog 都能帮助您深入理解和学习语言。就像在浏览器中拥有一位语言老师！

适用人群：

- 希望通过真实内容学习语言的语言学习者
- 希望更好理解外语文章的读者
- 任何想让网络浏览更具教育意义的人

加入我们不断增长的语言学习者社区，帮助塑造 Read Frog 的未来！

<div align="right">

[![][back-to-top]](#readme-top)

</div>

## 🤝 贡献

我们欢迎各种类型的贡献。

1. 向您的朋友和家人推广 Read Frog。
2. 报告[问题][issues-link]和反馈。
3. 贡献代码。

### 贡献代码

通过AI了解项目：[DeepWiki](https://deepwiki.com/mengxi-ream/read-frog)

查看[贡献指南](https://readfrog.app/en/tutorial/contribution)了解更多详情。

<a href="https://github.com/mengxi-ream/read-frog/graphs/contributors">
  <table>
    <tr>
      <th colspan="2">
        <br>
        <img src="https://contrib.rocks/image?repo=mengxi-ream/read-frog"><br>
        <br>
      </th>
    </tr>
    <tr>
      <td>
        <picture>
          <source media="(prefers-color-scheme: dark)" srcset="https://next.ossinsight.io/widgets/official/compose-recent-top-contributors/thumbnail.png?repo_id=967738751&image_size=auto&color_scheme=dark" width="373" height="auto">
          <img alt="Top Contributors of mengxi-ream/read-frog - Last 28 days" src="https://next.ossinsight.io/widgets/official/compose-recent-top-contributors/thumbnail.png?repo_id=967738751&image_size=auto&color_scheme=light" width="373" height="auto">
        </picture>
      </td>
      <td rowspan="2">
        <picture>
          <source media="(prefers-color-scheme: dark)" srcset="https://next.ossinsight.io/widgets/official/compose-last-28-days-stats/thumbnail.png?repo_id=967738751&image_size=4x7&color_scheme=dark" width="655" height="auto">
          <img alt="Performance Stats of mengxi-ream/read-frog - Last 28 days" src="https://next.ossinsight.io/widgets/official/compose-last-28-days-stats/thumbnail.png?repo_id=967738751&image_size=auto&color_scheme=light" width="655" height="auto">
        </picture>
      </td>
    </tr>
  </table>
</a>

<div align="right">

[![][back-to-top]](#readme-top)

</div>

## ❤️ 赞助者

每一笔捐赠都帮助我们构建更好的语言学习体验。感谢您支持我们的使命！

[![][sponsor-image]][sponsor-link]

<div align="right">

[![][back-to-top]](#readme-top)

</div>

<!-- LINK GROUP -->

[back-to-top]: https://img.shields.io/badge/-回到顶部-151515?style=flat-square
[chrome-store-link]: https://chromewebstore.google.com/detail/read-frog-open-source-ai/modkelfkcfjpgbfmnbnllalkiogfofhb
[chrome-users-shield]: https://img.shields.io/chrome-web-store/users/modkelfkcfjpgbfmnbnllalkiogfofhb?style=flat-square&label=Chrome%20用户&color=orange&labelColor=black
[chrome-version-shield]: https://img.shields.io/chrome-web-store/v/modkelfkcfjpgbfmnbnllalkiogfofhb?style=flat-square&label=Chrome&labelColor=black
[contributors-link]: https://github.com/mengxi-ream/read-frog/graphs/contributors
[contributors-shield]: https://img.shields.io/github/contributors/mengxi-ream/read-frog?style=flat-square&labelColor=black
[crxsoso-link]: https://www.crxsoso.com/webstore/detail/modkelfkcfjpgbfmnbnllalkiogfofhb
[discord-link]: https://discord.gg/ej45e3PezJ
[discord-shield]: https://img.shields.io/discord/1371229720942874646?style=flat-square&label=Discord&logo=discord&logoColor=white&color=5865F2&labelColor=black
[discord-shield-badge]: https://img.shields.io/badge/聊天-Discord-5865F2?style=for-the-badge&logo=discord&logoColor=white&labelColor=black
[edge-store-link]: https://microsoftedge.microsoft.com/addons/detail/read-frog-open-source-a/cbcbomlgikfbdnoaohcjfledcoklcjbo
[edge-users-shield]: https://img.shields.io/badge/dynamic/json?style=flat-square&logo=microsoft-edge&label=Edge%20用户&query=%24.activeInstallCount&url=https%3A%2F%2Fmicrosoftedge.microsoft.com%2Faddons%2Fgetproductdetailsbycrxid%2Fcbcbomlgikfbdnoaohcjfledcoklcjbo&labelColor=black
[edge-version-shield]: https://img.shields.io/badge/dynamic/json?style=flat-square&logo=microsoft-edge&label=Edge&query=%24.version&url=https%3A%2F%2Fmicrosoftedge.microsoft.com%2Faddons%2Fgetproductdetailsbycrxid%2Fcbcbomlgikfbdnoaohcjfledcoklcjbo&labelColor=black&prefix=v
[extension-release-shield]: https://img.shields.io/github/package-json/v/mengxi-ream/read-frog?filename=apps%2Fextension%2Fpackage.json&style=flat-square&label=扩展&color=brightgreen&labelColor=black
[github-release-link]: https://github.com/mengxi-ream/read-frog/releases
[github-star-link]: https://github.com/mengxi-ream/read-frog/stargazers
[image-banner]: /assets/store/large-promo-tile.png
[sponsor-image]: ./assets/sponsorkit/sponsors.svg
[image-star]: ./assets/star.png
[issues-link]: https://github.com/mengxi-ream/read-frog/issues
[issues-shield]: https://img.shields.io/github/issues/mengxi-ream/read-frog?style=flat-square&labelColor=black
[last-commit-shield]: https://img.shields.io/github/last-commit/mengxi-ream/read-frog?style=flat-square&label=commit&labelColor=black
[sponsor-link]: https://github.com/sponsors/mengxi-ream
[sponsor-shield]: https://img.shields.io/github/sponsors/mengxi-ream?style=flat-square&label=赞助&color=EA4AAA&labelColor=black
[star-history-link]: https://www.star-history.com/#mengxi-ream/read-frog&Timeline
[star-history-shield]: https://img.shields.io/github/stars/mengxi-ream/read-frog?style=flat-square&label=stars&color=yellow&labelColor=black
[website]: https://readfrog.app
[website-release-shield]: https://img.shields.io/github/package-json/v/mengxi-ream/read-frog?filename=apps%2Fwebsite%2Fpackage.json&style=flat-square&label=网站&color=success&labelColor=black
[wechat-link]: ./assets/wechat-account.jpg
[wechat-shield-badge]: https://img.shields.io/badge/聊天-微信-07C160?style=for-the-badge&logo=wechat&logoColor=white&labelColor=black
