# Check if SKIP_PRE_PUSH environment variable is set
if [ "$SKIP_PRE_PUSH" = "true" ]; then
  echo "⏭️  Skipping pre-push checks (SKIP_PRE_PUSH=true)"
  exit 0
fi

# Skip environment validation for all commands in this script
export SKIP_ENV_VALIDATION=true

echo "🔍 Checking for uncommented eruda imports..."
if grep -r "import.*eruda" src/ --include="*.ts" --include="*.tsx" --include="*.js" --include="*.jsx" | grep -v ":.*[[:space:]]*//.*import.*eruda"; then
  echo "❌ Found uncommented eruda imports in the codebase!"
  echo "Please comment out or remove eruda imports before pushing."
  exit 1
else
  echo "✅ No uncommented eruda imports found."
fi

pnpm syncpack:lint
pnpm type-check
pnpm build

# Check if SKIP_FREE_API environment variable is set
if [ "$SKIP_FREE_API" = "true" ]; then
  pnpm test -- --exclude="**/free-api.test.ts"
else
  pnpm test
fi